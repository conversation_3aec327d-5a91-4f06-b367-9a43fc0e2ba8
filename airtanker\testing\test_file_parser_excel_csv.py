#!/usr/bin/env python3
"""
Test script for FileParserService Excel and CSV processing functionality
"""

import sys
import os
import io
import json
import pandas as pd
from openpyxl import Workbook

# Add the parent directory to Python path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.FileParserService import FileParserService

def create_test_excel_file():
    """Create a test Excel file with multiple sheets"""
    wb = Workbook()
    
    # First sheet - Employee timesheet data
    ws1 = wb.active
    ws1.title = "Timesheet"
    
    # Add headers
    ws1['A1'] = 'Date'
    ws1['B1'] = 'Employee Name'
    ws1['C1'] = 'Customer Name'
    ws1['D1'] = 'Hours'
    ws1['E1'] = 'Rate Type'
    
    # Add sample data
    data = [
        ['06-16-2025', '<PERSON>', 'ACME Corp', 8.0, 'R<PERSON>'],
        ['06-17-2025', '<PERSON>', 'ACME Corp', 8.5, 'R<PERSON>'],
        ['06-18-2025', '<PERSON>', 'Beta Industries', 7.5, 'REG'],
        ['06-19-2025', '<PERSON>', 'Beta Industries', 9.0, 'OT'],
    ]
    
    for row_idx, row_data in enumerate(data, start=2):
        for col_idx, value in enumerate(row_data, start=1):
            ws1.cell(row=row_idx, column=col_idx, value=value)
    
    # Second sheet - Summary data
    ws2 = wb.create_sheet("Summary")
    ws2['A1'] = 'Total Hours'
    ws2['B1'] = 33.0
    ws2['A2'] = 'Total Employees'
    ws2['B2'] = 2
    
    # Save to bytes
    excel_buffer = io.BytesIO()
    wb.save(excel_buffer)
    excel_buffer.seek(0)
    
    return excel_buffer.getvalue()

def create_test_csv_file():
    """Create a test CSV file"""
    csv_data = """Date,Employee Name,Customer Name,Hours,Rate Type
06-16-2025,John Doe,ACME Corp,8.0,REG
06-17-2025,John Doe,ACME Corp,8.5,REG
06-18-2025,Jane Smith,Beta Industries,7.5,REG
06-19-2025,Jane Smith,Beta Industries,9.0,OT"""
    
    return csv_data.encode('utf-8')

def test_excel_conversion():
    """Test Excel to JSON conversion"""
    print("🧪 Testing Excel to JSON conversion...")
    
    try:
        # Create test Excel file
        excel_content = create_test_excel_file()
        
        # Initialize FileParserService
        parser = FileParserService()
        
        # Convert Excel to JSON
        json_result = parser._convert_excel_to_structured_json(excel_content, "test_timesheet.xlsx")
        
        # Parse the JSON to verify structure
        parsed_json = json.loads(json_result)
        
        print("✅ Excel conversion successful!")
        print(f"📊 Found {len(parsed_json['sheets'])} sheets")
        
        for sheet in parsed_json['sheets']:
            print(f"   - Sheet: {sheet['sheet_name']} (index: {sheet['sheet_index']}) with {len(sheet['data'])} rows")
            
            # Show first few rows of data
            if sheet['data']:
                print(f"     Sample data: {sheet['data'][0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel conversion failed: {e}")
        return False

def test_csv_conversion():
    """Test CSV to text conversion"""
    print("\n🧪 Testing CSV to text conversion...")
    
    try:
        # Create test CSV file
        csv_content = create_test_csv_file()
        
        # Initialize FileParserService
        parser = FileParserService()
        
        # Convert CSV to text
        text_result = parser._convert_csv_to_text(csv_content, "test_timesheet.csv")
        
        print("✅ CSV conversion successful!")
        print(f"📄 CSV content ({len(text_result)} characters):")
        print("--- CSV Content ---")
        print(text_result[:200] + "..." if len(text_result) > 200 else text_result)
        print("--- End CSV Content ---")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV conversion failed: {e}")
        return False

def test_file_type_detection():
    """Test file type detection logic"""
    print("\n🧪 Testing file type detection...")
    
    test_cases = [
        ("test.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Excel"),
        ("test.xls", "application/vnd.ms-excel", "Excel"),
        ("test.csv", "text/csv", "CSV"),
        ("test.pdf", "application/pdf", "Other"),
        ("test.png", "image/png", "Other"),
    ]
    
    for filename, content_type, expected_type in test_cases:
        file_extension = filename.lower().split('.')[-1] if '.' in filename else ''
        content_type_lower = content_type.lower() if content_type else ''
        
        if (file_extension in ['xlsx', 'xls'] or 
            'spreadsheet' in content_type_lower or 
            'excel' in content_type_lower):
            detected_type = "Excel"
        elif (file_extension == 'csv' or 
              'csv' in content_type_lower):
            detected_type = "CSV"
        else:
            detected_type = "Other"
        
        status = "✅" if detected_type == expected_type else "❌"
        print(f"   {status} {filename} ({content_type}) -> {detected_type}")

def main():
    """Run all tests"""
    print("🚀 Starting FileParserService Excel/CSV Tests")
    print("=" * 50)
    
    # Test file type detection
    test_file_type_detection()
    
    # Test Excel conversion
    excel_success = test_excel_conversion()
    
    # Test CSV conversion
    csv_success = test_csv_conversion()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   Excel conversion: {'✅ PASS' if excel_success else '❌ FAIL'}")
    print(f"   CSV conversion: {'✅ PASS' if csv_success else '❌ FAIL'}")
    
    if excel_success and csv_success:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print("\n💥 Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
