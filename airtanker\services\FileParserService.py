from typing import Dict, List
from main import airtanker_app
from services.DatabaseService import DatabaseService
from services.GeminiService import GeminiService
from services.CustomerService import CustomerService
from models.NameMatch import Status, Origin, NameMatch
from google.genai import types
import base64
import json
import pandas as pd
import numpy as np
import io
import csv
from openpyxl import load_workbook

class FileParserService:

    def __init__(self):
        pass

    def _convert_excel_to_structured_json(self, file_content: bytes, filename: str) -> str:
        """
        Convert Excel file to structured JSON format.

        Returns a JSON string with the following structure:
        {
            "sheets": [
                {
                    "sheet_name": "Sheet1",
                    "sheet_index": 0,
                    "data": [
                        {"col1": "value1", "col2": "value2", ...},
                        ...
                    ]
                },
                ...
            ]
        }
        """
        try:
            # Load the workbook from bytes
            workbook = load_workbook(io.BytesIO(file_content), data_only=True)

            structured_data = {
                "sheets": []
            }

            for sheet_index, sheet_name in enumerate(workbook.sheetnames):
                sheet = workbook[sheet_name]

                # Skip hidden sheets
                if sheet.sheet_state != "visible":
                    continue

                # Convert sheet to DataFrame
                # Get all data from the sheet
                data = []
                for row in sheet.iter_rows(values_only=True):
                    # Skip completely empty rows
                    if all(cell is None for cell in row):
                        continue
                    data.append(row)

                if not data:
                    continue

                # Create DataFrame from the data
                df = pd.DataFrame(data)

                # Use first row as headers if it contains strings
                if len(df) > 0:
                    first_row = df.iloc[0]
                    if any(isinstance(val, str) and val.strip() for val in first_row if val is not None):
                        # First row looks like headers
                        df.columns = [str(col) if col is not None else f"Column_{i}" for i, col in enumerate(first_row)]
                        df = df.drop(0).reset_index(drop=True)
                    else:
                        # No clear headers, use generic column names
                        df.columns = [f"Column_{i}" for i in range(len(df.columns))]

                # Convert DataFrame to records (list of dictionaries)
                records = df.to_dict(orient='records')

                # Clean up the records - convert NaN to None and handle data types
                cleaned_records = []
                for record in records:
                    cleaned_record = {}
                    for key, value in record.items():
                        if pd.isna(value):
                            cleaned_record[key] = None
                        elif isinstance(value, (pd.Timestamp, pd.Timedelta)):
                            cleaned_record[key] = str(value)
                        else:
                            cleaned_record[key] = value
                    cleaned_records.append(cleaned_record)

                sheet_data = {
                    "sheet_name": sheet_name,
                    "sheet_index": sheet_index,
                    "data": cleaned_records
                }

                structured_data["sheets"].append(sheet_data)

            return json.dumps(structured_data, indent=2, default=str)

        except Exception as e:
            print(f"Error converting Excel file {filename} to JSON: {e}")
            airtanker_app.logger.exception(f"Error converting Excel file {filename} to JSON")
            raise Exception(f"Failed to convert Excel file to structured JSON: {e}")

    def _convert_csv_to_text(self, file_content: bytes, filename: str) -> str:
        """
        Convert CSV file to text format for Gemini processing.

        Returns the CSV content as a string with some basic formatting.
        """
        try:
            # Try to decode the file content
            try:
                text_content = file_content.decode('utf-8')
            except UnicodeDecodeError:
                # Try with different encoding
                text_content = file_content.decode('latin-1')

            # Parse CSV to validate and clean it
            csv_reader = csv.reader(io.StringIO(text_content))
            rows = list(csv_reader)

            if not rows:
                return "Empty CSV file"

            # Convert back to clean CSV format
            output = io.StringIO()
            csv_writer = csv.writer(output)
            csv_writer.writerows(rows)

            return output.getvalue()

        except Exception as e:
            print(f"Error converting CSV file {filename} to text: {e}")
            airtanker_app.logger.exception(f"Error converting CSV file {filename} to text")
            raise Exception(f"Failed to convert CSV file to text: {e}")

    def parse_customer_timesheets(self, *, files, template_ids, db_service: DatabaseService) -> pd.DataFrame:

        # db_service = DatabaseService()
        # db_service.connect() # We are already connected...
        
        all_dfs = []
        templates = db_service.get_templates()
        template_map = {int(template['id']): template for template in templates}

        # TODO: Check if we need to retrieve the customers before the call to gemini, so we can send it to gemini and have it match the customers for us.
        
        for idx, (file, template_id) in enumerate(zip(files, template_ids)):
            print(f"\n=== Processing File {idx + 1} ===")
            print(f"Filename: {file.filename}")
            print(f"Template ID: {template_id}")

            # Check if file has already been processed
            file_id, file_already_processed = db_service.insert_filename_get_id(filename=file.filename, sheet_name='', source_type='Customer Timesheets')
            if file_already_processed:
                print(f"File {file.filename} has already been processed. Skipping...")
                continue

            # Get template data
            template = template_map.get(int(template_id))
            if not template:
                print(f"ERROR: Template with ID {template_id} not found!")
                continue

            # Read file content
            file.seek(0)  # Reset file pointer
            file_content = file.read()

            # Prepare the AI prompt with template data
            ai_prompt = template['prompt']
            response_schema = template['response_schema']

            # Determine file type and process accordingly
            file_extension = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
            content_type = file.content_type.lower() if file.content_type else ''

            print(f"File extension: {file_extension}, Content type: {content_type}")

            # Process file based on type
            if (file_extension in ['xlsx', 'xls'] or
                'spreadsheet' in content_type or
                'excel' in content_type):

                print("📊 Processing Excel file - converting to structured JSON...")
                try:
                    structured_json = self._convert_excel_to_structured_json(file_content, file.filename)
                    complete_prompt = f"File Name: {file.filename}\nFile Type: Excel (converted to JSON)\n\n{ai_prompt}\n\nStructured Data:\n{structured_json}"

                    # For Excel files converted to JSON, send as text
                    parts = [
                        types.Part.from_text(text=complete_prompt),
                    ]

                except Exception as e:
                    print(f"❌ Error processing Excel file: {e}")
                    continue

            elif (file_extension == 'csv' or
                  'csv' in content_type):

                print("📄 Processing CSV file - converting to text...")
                try:
                    csv_text = self._convert_csv_to_text(file_content, file.filename)
                    complete_prompt = f"File Name: {file.filename}\nFile Type: CSV\n\n{ai_prompt}\n\nCSV Data:\n{csv_text}"

                    # For CSV files, send as text
                    parts = [
                        types.Part.from_text(text=complete_prompt),
                    ]

                except Exception as e:
                    print(f"❌ Error processing CSV file: {e}")
                    continue

            else:
                # For other file types (PDF, images, etc.), use the original base64 approach
                print(f"📎 Processing {file_extension.upper()} file - using base64 encoding...")
                file_base64 = base64.b64encode(file_content).decode('utf-8')
                complete_prompt = f"File Name: {file.filename}\nMime Type: {file.content_type}\n\n{ai_prompt}"

                parts = [
                    types.Part.from_bytes(
                        mime_type=file.content_type,
                        data=file_base64,
                    ),
                    types.Part.from_text(text=complete_prompt),
                ]

            # Make the AI call
            print(f"\n--- Making AI Call ---")
            try:
                gemini_service = GeminiService()

                # Generate AI response
                print("🤖 Sending prompt to Gemini...")
                print("⏳ This may take a few moments...")

                ai_response = gemini_service.generate_content(parts, response_schema)

                print(f"✅ AI Response received ({len(ai_response)} characters)")

                # Eventhough we are asking gemini to return json when sending back the reponse, it sends it in str chunks, so we need to reparse it.
                try:
                    parsed_response = json.loads(ai_response)
                    print(f"\n--- Parsed JSON Response to {type(parsed_response)} ---")
                    print(json.dumps(parsed_response, indent=2))

                    # Validate against expected schema
                    if 'timeentries' in parsed_response:
                        print("✅ Response appears to contain timesheet data")
                    else:
                        raise Exception("Response did not match expected timesheet format")
                    
                except Exception as e:
                    print(f"⚠️  Warning: Error parsing AI response: {e}")
                    raise Exception(f"Error parsing AI response: {e}")

            except Exception as ai_error:
                print(f"❌ Error calling Gemini AI: {ai_error}")
                airtanker_app.logger.exception("Gemini AI call failed")

            timeentries = parsed_response['timeentries']
            # Create a df from the timeentries
            df = pd.DataFrame(timeentries)
            # Parse the date
            df["Date"] = pd.to_datetime(df["Date"], format="%m-%d-%Y")
            # Add a new column with the file_id for all rows of this df
            df["FileID"] = file_id
            df["FileName"] = file.filename

            all_dfs.append(df) # Add the df to the list of all dfs
            print("=" * 50)

        # Now we have parsed all the files
        if not all_dfs:
            print("No new data to process. Returning empty DataFrame.")
            return pd.DataFrame()
        
        # db_service.disconnect() # We should disconnect in the calling function.
        
        combined_df = pd.concat(all_dfs, ignore_index=True) # glue all dfs together
        return combined_df
    
    def parse_customer_timesheets_dummy(self, *, files, template_ids, db_service: DatabaseService) -> pd.DataFrame:
        # 1) Your dummy list of dicts (from your first message)
        timeentries = [
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 7.91,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 8.80,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 8.27,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 8.02,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 7.48,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "AtomTech - Clarkston",
                "CustomerID": "2134",
                "RateType": "TRT",
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "AtomTech - Clarkston",
                "CustomerID": "2134",
                "RateType": "OT",
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "AtomTech - Clarkston",
                "CustomerID": "2134",
                "RateType": "DBT",
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "AtomTech - Clarkston",
                "CustomerID": "2134",
                "RateType": "REG",
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "Roseville",
                "CustomerID": "2134",
                "RateType": "REG",
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-21-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 9.7,
                "EmployeeName": "Antonio Herrera Avila",
                "Location": "Fibro Laepple Technology Inc"
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 10.12,
                "EmployeeName": "Antonio Herrera Avila",
                "Location": "Fibro Laepple Technology Inc"
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 10.08,
                "EmployeeName": "Antonio Herrera Avila",
                "Location": "Fibro Laepple Technology Inc"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 10.38,
                "EmployeeName": "Antonio Herrera Avila",
                "Location": "Fibro Laepple Technology Inc"
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 8.0,
                "EmployeeName": "Ciprian Stinguta",
                "Location": "Fibro Laepple Technology Inc."
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 9.5,
                "EmployeeName": "Ciprian Stinguta",
                "Location": "Fibro Laepple Technology Inc."
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 9.5,
                "EmployeeName": "Ciprian Stinguta",
                "Location": "Fibro Laepple Technology Inc."
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Ciprian Stinguta",
                "Location": "Fibro Laepple Technology Inc."
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 9.0,
                "EmployeeName": "Jesus Carranza",
                "CustomerID": "24"
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 9.0,
                "EmployeeName": "Jesus Carranza",
                "CustomerID": "24"
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 9.0,
                "EmployeeName": "Jesus Carranza",
                "CustomerID": "24"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 9.0,
                "EmployeeName": "Jesus Carranza",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 4.0,
                "EmployeeName": "Jesus Carranza",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 2.5,
                "EmployeeName": "Jesus Carranza",
                "RateType": "OT",
                "CustomerID": "24"
            },
            {
                "Date": "06-21-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Jesus Carranza",
                "RateType": "OT",
                "CustomerID": "24"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Jesus Carranza",
                "RateType": "DBT",
                "CustomerID": "24"
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 8.8333,
                "EmployeeName": "Paul Kopcak",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 9.1667,
                "EmployeeName": "Paul Kopcak",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 2.0,
                "EmployeeName": "Paul Kopcak",
                "RateType": "TRT",
                "CustomerID": "24"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 12.0,
                "EmployeeName": "Paul Kopcak",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 2.0,
                "EmployeeName": "Paul Kopcak",
                "RateType": "TRT",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Paul Kopcak",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 2.0,
                "EmployeeName": "Paul Kopcak",
                "RateType": "OT",
                "CustomerID": "24"
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Joseph Lesage",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Joseph Lesage",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 10.1667,
                "EmployeeName": "Joseph Lesage",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 2.0,
                "EmployeeName": "Joseph Lesage",
                "RateType": "TRT",
                "CustomerID": "24"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 9.8333,
                "EmployeeName": "Joseph Lesage",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 2.1667,
                "EmployeeName": "Joseph Lesage",
                "RateType": "OT",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 2.0,
                "EmployeeName": "Joseph Lesage",
                "RateType": "TRT",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 11.8333,
                "EmployeeName": "Joseph Lesage",
                "RateType": "DBT",
                "CustomerID": "24"
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 8.3333,
                "EmployeeName": "Senad Salkic",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 7.6667,
                "EmployeeName": "Senad Salkic",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 7.6667,
                "EmployeeName": "Senad Salkic",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 7.6667,
                "EmployeeName": "Senad Salkic",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 7.6667,
                "EmployeeName": "Senad Salkic",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-21-2025",
                "CustomerReportedHours": 6.8333,
                "EmployeeName": "Senad Salkic",
                "RateType": "OT",
                "CustomerID": "24"
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 9.0,
                "EmployeeName": "Chris Turner",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 8.5,
                "EmployeeName": "Chris Turner",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 9.0,
                "EmployeeName": "Chris Turner",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 9.0,
                "EmployeeName": "Chris Turner",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 4.5,
                "EmployeeName": "Chris Turner",
                "RateType": "REG",
                "CustomerID": "24"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 4.5,
                "EmployeeName": "Chris Turner",
                "RateType": "OT",
                "CustomerID": "24"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 8.0,
                "EmployeeName": "Abraham Israel Salazar Garcia",
                "RateType": "REG"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 12.0,
                "EmployeeName": "Abraham Israel Salazar Garcia",
                "RateType": "DBT"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 40.0,
                "EmployeeName": "Marco Aurelio Gonzalez Patino",
                "RateType": "REG"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 16.0,
                "EmployeeName": "Marco Aurelio Gonzalez Patino",
                "RateType": "OT"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 40.0,
                "EmployeeName": "Bradley James Anderton",
                "RateType": "REG"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 20.4,
                "EmployeeName": "Bradley James Anderton",
                "RateType": "OT"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 35.3,
                "EmployeeName": "Daniel James Green-Byrne",
                "RateType": "REG"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 25.0,
                "EmployeeName": "Juan Rossouw",
                "RateType": "REG"
            },
            {
                "Date": "06-22-2025",
                "CustomerReportedHours": 36.9,
                "EmployeeName": "Layla Carrum Uribe",
                "RateType": "REG"
            }
        ]
        
        # 2) Wrap it in the same shape as the parsed AI response:
        parsed_response = {"timeentries": timeentries}
        
        # 3) Create a DataFrame just like your function does:
        df = pd.DataFrame(parsed_response["timeentries"])
        
        # 4) Parse the Date column into datetime:
        df["Date"] = pd.to_datetime(df["Date"], format="%m-%d-%Y")
        
        # (Optional) 5) If you wanted to add a FileID column:
        df["FileID"] = 1234
        df["FileName"] = "test_file.pdf"
        
        print(df)
        return df
    
    def parse_customer_names(self, *, df: pd.DataFrame, all_active_work_orders, db_service: DatabaseService) -> {pd.DataFrame}:
        # db_service = DatabaseService()
        # db_service.connect() # we are already connected...

        # If no column CustomerName, return df
        if 'CustomerName' not in df.columns:
            print("No CustomerName column found. Returning original DataFrame.")
            return df
        
        # Get customer names from database for name replacement
        all_db_customers = db_service.get_customers()
        customer_id_to_name = {
            str(customer['CustomerID']): customer['CustomerName']
            for customer in all_db_customers
        }

        # build a mask of rows with a non-empty, non-null CustomerName
        name_mask = df['CustomerName'].fillna('').str.strip().ne('')

        missing_mask = ~name_mask
        rows_missing_name = df[missing_mask].copy()
        
        if not rows_missing_name.empty:
            # rows_missing_name['ErrorMessage'] = "Customer name is missing."
            print(f"Found {len(rows_missing_name)} rows with missing customer name. Skipping them.")

        df = df[name_mask].copy()

        # Filter for lines that do not have a CustomerID assigned
        if 'CustomerID' in df.columns:
            needs_id_mask = df['CustomerID'].isna() | (df['CustomerID'] == '')
            lines_without_customer_id = df[needs_id_mask]
        else:
            lines_without_customer_id = df

        # Nothing to do?
        if lines_without_customer_id.empty:
            print("No lines without customer ID. Returning original DataFrame as it is ready for next step.")
            return df        

        customer_names = lines_without_customer_id["CustomerName"].unique().tolist()

        # extract the distinct customers (name and id) from all_active_work_order_entries
        seen = set()
        active_customers = []
        for wo in all_active_work_orders:
            key = (wo['CustomerName'], wo['CustomerID'])
            if key not in seen:
                seen.add(key)
                active_customers.append(
                    {
                        'customer_id': wo['CustomerID'],
                        'customer_name': wo['CustomerName'],
                    }
                )

        # use the customer service to match the names
        customer_matches = CustomerService(db_service).match_customers(customer_names, active_customers)
        # customer_matches should return somthing like this:
        # [
        #   NameMatch(name='ACME Corporation', status=Status.MATCHED, origin=Origin.ACTIVE_WO, ids=[101]),
        #   NameMatch(name='Beta Industries, Inc.', status=Status.AMBIGUOUS, origin=Origin.ACTIVE_WO, ids=[102, 103]),
        #   NameMatch(name='Delta Co', status=Status.MATCHED, origin=Origin.DATABASE, ids=[104]),
        #   NameMatch(name='Epsilon Inc', status=Status.AMBIGUOUS, origin=Origin.DATABASE, ids=[105, 106]),
        #   NameMatch(name='Gamma & Sons LLC', status=Status.NOT_FOUND, origin=None, ids=None),
        # ]

        print(f"Customer Matches:\n{customer_matches}")

        df_result = df.copy()

        # Initialize new columns if they don't exist
        if 'CustomerID' not in df_result.columns:
            df_result['CustomerID'] = None
        if 'PossibleCustomerIDs' not in df_result.columns:
            df_result['PossibleCustomerIDs'] = None
        if 'ErrorMessage' not in df_result.columns:
            df_result['ErrorMessage'] = None

        # Create a mapping from customer names to match results for quick lookup
        customer_match_map = {match.name: match for match in customer_matches}

        # Handle results for each row that needs customer ID assignment
        for idx, row in df_result.iterrows():
            customer_name = row['CustomerName']
            match_result = customer_match_map.get(customer_name)

            if not match_result:
                # This shouldn't happen, but handle gracefully (Just in case bad template configured)
                df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} was not processed during matching."
                airtanker_app.logger.error(f"Customer {customer_name} was not processed during matching. Error in FileParserService.parse_customer_names()")
                continue

            # Handle different match scenarios
            if match_result.status == Status.MATCHED:
                if match_result.origin == Origin.ACTIVE_WO:
                    # Perfect match from active work orders - assign CustomerID directly
                    df_result.at[idx, 'CustomerID'] = match_result.ids[0]
                    # Update CustomerName with the correct DB name
                    if str(match_result.ids[0]) in customer_id_to_name:
                        df_result.at[idx, 'CustomerName'] = customer_id_to_name[str(match_result.ids[0])]

                elif match_result.origin == Origin.DATABASE:
                    # Match from database - add to PossibleCustomerIDs with warning
                    df_result.at[idx, 'PossibleCustomerIDs'] = str(match_result.ids[0])
                    df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} not found on active work orders. Selected {customer_name} from database. Please make sure the customer has an active work order and reupload file. Or skip this record."
                    # Update CustomerName with the correct DB name
                    if str(match_result.ids[0]) in customer_id_to_name:
                        df_result.at[idx, 'CustomerName'] = customer_id_to_name[str(match_result.ids[0])]

            elif match_result.status == Status.AMBIGUOUS:
                # Multiple matches - add all IDs to PossibleCustomerIDs
                ids_str = ','.join(map(str, match_result.ids))
                df_result.at[idx, 'PossibleCustomerIDs'] = ids_str

                if match_result.origin == Origin.ACTIVE_WO:
                    df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} has multiple matches. Please select the correct one."
                elif match_result.origin == Origin.DATABASE:
                    df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} has multiple matches. Select the correct one and make sure the customer has an active work order. Or skip this record."

            elif match_result.status == Status.NOT_FOUND:
                # No match found
                df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} not found on internal records. Select one from this list, or verify work order and customer name and reupload file. Maybe a custom template for this file is needed."

        # Print summary of results
        matched_count = len(df_result[(df_result['CustomerID'].notna()) & (df_result['CustomerID'] != '')])
        error_count = len(df_result[df_result['ErrorMessage'].notna()])
        print(f"📈 Summary: {matched_count} rows with direct CustomerID assignment, {error_count} rows with errors/warnings")

        # db_service.disconnect() # we should disconnect in the calling function.

        # Before concatenating rows_missing_name, check if any of them have CustomerID but no CustomerName
        # and populate CustomerName from the database mapping
        if not rows_missing_name.empty and 'CustomerID' in rows_missing_name.columns:
            # Create a mask for rows that have CustomerID but no CustomerName
            has_customer_id_mask = rows_missing_name['CustomerID'].notna() & (rows_missing_name['CustomerID'] != '')
            rows_with_id_no_name = rows_missing_name[has_customer_id_mask].copy()

            if not rows_with_id_no_name.empty:
                print(f"Found {len(rows_with_id_no_name)} rows with CustomerID but no CustomerName. Adding CustomerName from database.")

                # Populate CustomerName for these rows using the customer_id_to_name mapping
                # Remember that CustomerID from original df is a string, so convert to string for lookup
                for idx in rows_with_id_no_name.index:
                    customer_id_str = str(rows_missing_name.at[idx, 'CustomerID'])
                    if customer_id_str in customer_id_to_name:
                        rows_missing_name.at[idx, 'CustomerName'] = customer_id_to_name[customer_id_str]
                        # print(f"Added CustomerName '{customer_id_to_name[customer_id_str]}' for CustomerID '{customer_id_str}'")
                    else:
                        print(f"Warning: CustomerID '{customer_id_str}' not found in database customer mapping")

        df_result = pd.concat([df_result, rows_missing_name], ignore_index=True)

        return df_result


    def parse_employee_names(self, *, df: pd.DataFrame, all_active_work_orders, db_service: DatabaseService) -> pd.DataFrame:

        if 'EmployeeName' not in df.columns:
            print("No EmployeeName column found. Returning original DataFrame.")
            return df

        # df should not have EmployeeID assigned

        # Extract the distinct employee names from the df
        employee_names = df["EmployeeName"].unique().tolist()

        # extract the distinct employees (name and id) from all_active_work_order_entries
        seen = set()
        active_employees = []
        for wo in all_active_work_orders:
            key = (wo['FullName'], wo['EmployeeID'])
            if key not in seen:
                seen.add(key)
                active_employees.append(
                    {
                        'employee_id': wo['EmployeeID'],
                        'employee_name': wo['FullName'],
                    }
                )

        # use the employee service to match the names
        from services.EmployeeService import EmployeeService
        employee_matches = EmployeeService(db_service).match_employees(employee_names, active_employees)
        # employee_matches should return something like this:
        # [
        #   NameMatch(name='John Doe', status=Status.MATCHED, origin=Origin.ACTIVE_WO, ids=[101]),
        #   NameMatch(name='Jane Smith', status=Status.AMBIGUOUS, origin=Origin.ACTIVE_WO, ids=[102, 103]),
        #   NameMatch(name='Bob Johnson', status=Status.MATCHED, origin=Origin.DATABASE, ids=[104]),
        #   NameMatch(name='Alice Brown', status=Status.AMBIGUOUS, origin=Origin.DATABASE, ids=[105, 106]),
        #   NameMatch(name='Charlie Wilson', status=Status.NOT_FOUND, origin=None, ids=None),
        # ]

        # print(f"Employee Matches:\n{employee_matches}")

        df_result = df.copy()

        # Create a mapping from employee name to match result for quick lookup
        employee_match_dict = {match.name: match for match in employee_matches}

        # Create a mapping from employee ID to employee name for database employees
        employee_id_to_name = {}
        try:
            all_db_employees = db_service.get_all_employees()
            for emp in all_db_employees:
                employee_id_to_name[emp['EmployeeID']] = emp['FullName']
        except Exception as e:
            print(f"Error getting database employees: {e}")

        # Helper function to append error messages
        def append_error_message(df, idx, new_error):
            """Append a new error message to existing error message, if any."""
            existing_error = df.at[idx, 'ErrorMessage']
            if pd.isna(existing_error) or existing_error is None or existing_error == '':
                df.at[idx, 'ErrorMessage'] = new_error
            else:
                df.at[idx, 'ErrorMessage'] = f"{existing_error}; {new_error}"

        # Add columns for results
        df_result['EmployeeID'] = None
        df_result['PossibleEmployees'] = None
        # df_result['ErrorMessage'] = None
        if 'ErrorMessage' not in df_result.columns:
            df_result['ErrorMessage'] = None

        # Helper function to create employee dictionary
        def create_employee_dict(employee_id, employee_name):
            return {'EmployeeID': employee_id, 'EmployeeName': employee_name}

        # Helper function to get employee name by ID
        def get_employee_name_by_id(emp_id):
            # First check active employees
            for emp in active_employees:
                if emp['employee_id'] == emp_id:
                    return emp['employee_name']
            # Then check database employees
            return employee_id_to_name.get(emp_id, f"Employee {emp_id}")

        # Process each row in the dataframe
        for idx, row in df_result.iterrows():
            employee_name = row['EmployeeName']
            match_result = employee_match_dict.get(employee_name)

            if not match_result:
                # This shouldn't happen, but handle it gracefully
                append_error_message(df_result, idx, f"No match result found for employee {employee_name}")
                continue

            if match_result.status == Status.MATCHED:
                # Single match found
                if match_result.origin == Origin.ACTIVE_WO:
                    # Match from active work orders - assign EmployeeID
                    df_result.at[idx, 'EmployeeID'] = match_result.ids[0]

                elif match_result.origin == Origin.DATABASE:
                    # Match from database - add to PossibleEmployees with warning
                    emp_name = get_employee_name_by_id(match_result.ids[0])
                    df_result.at[idx, 'PossibleEmployees'] = [create_employee_dict(match_result.ids[0], emp_name)]
                    append_error_message(df_result, idx, f"Employee {employee_name} not found on active work orders. Selected {employee_name} from database. Please make sure the employee has an active work order and reupload file. Or skip this record.")
                    # # Update EmployeeName with the correct DB name
                    # if match_result.ids[0] in employee_id_to_name:
                    #     df_result.at[idx, 'EmployeeName'] = employee_id_to_name[match_result.ids[0]]

            elif match_result.status == Status.AMBIGUOUS:
                # Multiple matches found - add to PossibleEmployees as list of dictionaries
                possible_employees = []
                for emp_id in match_result.ids:
                    emp_name = get_employee_name_by_id(emp_id)
                    possible_employees.append(create_employee_dict(emp_id, emp_name))
                df_result.at[idx, 'PossibleEmployees'] = possible_employees
                origin_text = "active work orders" if match_result.origin == Origin.ACTIVE_WO else "database"
                append_error_message(df_result, idx, f"Multiple employees found for {employee_name} in {origin_text}, or low confidence match. Please select the correct employee or skip this record.")

            elif match_result.status == Status.NOT_FOUND:
                # No matches found
                append_error_message(df_result, idx, f"Employee {employee_name} not found in active work orders or database. Please verify the full name in Odoo and that the employee has an active work order, or add the employee to the system.")

        return df_result

    def parse_rate_types(self, *, df: pd.DataFrame, db_service: DatabaseService) -> pd.DataFrame:
        """
        Parse RateType column and add RateTypeID column to the DataFrame.

        If RateType column exists and has values, matches them against database rate types
        and adds a RateTypeID column. Rows without RateType values are skipped (not removed).

        Args:
            df: DataFrame that may contain a 'RateType' column
            db_service: DatabaseService instance for database operations

        Returns:
            DataFrame with RateTypeID column added where RateType matches were found
        """
        # Check if RateType column exists
        if 'RateType' not in df.columns:
            print("No RateType column found. Returning original DataFrame.")
            return df

        # Create a copy to avoid modifying the original DataFrame
        df_result = df.copy()

        # Initialize RateTypeID column with None
        df_result['RateTypeID'] = None

        # Get rate types from database and create a mapping dictionary
        try:
            rate_types = db_service.get_rate_types()
            rate_type_map = {}
            for rate_type in rate_types:
                # Convert to lowercase for case-insensitive matching
                rate_type_map[rate_type['RateType'].lower()] = rate_type['ID']

            print(f"Available rate types in database: {list(rate_type_map.keys())}")

        except Exception as e:
            print(f"Error retrieving rate types from database: {e}")
            return df_result

        # Process each row that has a RateType value
        processed_count = 0
        matched_count = 0

        for idx, row in df_result.iterrows():
            rate_type_value = row.get('RateType')

            # Skip rows without RateType value (None, empty string, or NaN)
            if pd.isna(rate_type_value) or rate_type_value == '' or rate_type_value is None:
                continue

            processed_count += 1

            # Convert to string and lowercase for matching
            rate_type_str = str(rate_type_value).strip().lower()

            # Look up the rate type ID
            rate_type_id = rate_type_map.get(rate_type_str)

            if rate_type_id is not None:
                df_result.at[idx, 'RateTypeID'] = rate_type_id
                matched_count += 1
            else:
                print(f"Warning: RateType '{rate_type_value}' not found in database")

        print(f"Rate type processing complete: {matched_count}/{processed_count} rate types matched")

        return df_result

    def parse_location(self, *, df: pd.DataFrame, db_service: DatabaseService) -> pd.DataFrame:
        # TODO: Parse location column and add LocationID column to the DataFrame.
        # For now receive df, add LocationID column if doesnt exists, fill with None if exists, and return df.
        if 'Location' not in df.columns:
            print("No Location column found. Returning original DataFrame.")
            return df

        df['LocationID'] = None

        print("parse_location not implemented yet")
        return df

    def parse_work_orders(self, *, df: pd.DataFrame, all_active_work_orders, db_service: DatabaseService) -> pd.DataFrame:
        """
        Parse work orders and fill ProjectID and WorkOrderID columns based on EmployeeID matching.

        Logic:
        1. If employee has exactly one active work order:
           - Fill ProjectID and WorkOrderID
           - If CustomerID is filled, verify it matches; add warning if not
        2. If employee has multiple active work orders:
           - Try to match by CustomerID first (only if CustomerID column is filled)
           - If still multiple matches, try to match by date
           - If still multiple matches, save them to PossibleWorkOrders column for user selection
        3. If employee not found in active work orders, add error to ErrorMessage column

        Args:
            df: DataFrame with EmployeeID column
            all_active_work_orders: List of dictionaries from get_active_work_orders
            db_service: DatabaseService instance

        Returns:
            DataFrame with ProjectID, WorkOrderID, and PossibleWorkOrders columns added
        """
        print("Starting work order parsing...")

        # Create a copy to avoid modifying the original DataFrame
        df_result = df.copy()

        # Initialize new columns
        if 'ProjectID' not in df_result.columns:
            df_result['ProjectID'] = None
        if 'ProjectName' not in df_result.columns:
            df_result['ProjectName'] = None
        if 'WorkOrderID' not in df_result.columns:
            df_result['WorkOrderID'] = None
        if 'WorkOrderNumber' not in df_result.columns:
            df_result['WorkOrderNumber'] = None
        if 'CustomerID' not in df_result.columns:
            df_result['CustomerID'] = None
        if 'CustomerName' not in df_result.columns:
            df_result['CustomerName'] = None
        if 'PossibleWorkOrders' not in df_result.columns:
            df_result['PossibleWorkOrders'] = None
        if 'ErrorMessage' not in df_result.columns:
            df_result['ErrorMessage'] = None
        if 'WarningMessage' not in df_result.columns:
            df_result['WarningMessage'] = None

        # Helper function to append error messages
        def append_error_message(df, idx, new_error):
            """Append a new error message to existing error message, if any."""
            existing_error = df.at[idx, 'ErrorMessage']
            if pd.isna(existing_error) or existing_error is None or existing_error == '':
                df.at[idx, 'ErrorMessage'] = new_error
            else:
                df.at[idx, 'ErrorMessage'] = f"{existing_error}; {new_error}"

        # Create a mapping from EmployeeID to work orders for quick lookup
        employee_work_orders = {}
        for wo in all_active_work_orders:
            employee_id = wo['EmployeeID']
            if employee_id not in employee_work_orders:
                employee_work_orders[employee_id] = []
            employee_work_orders[employee_id].append(wo)

        processed_count = 0
        matched_count = 0
        multiple_matches_count = 0
        not_found_count = 0

        # Process each row in the dataframe
        for idx, row in df_result.iterrows():
            employee_id = row.get('EmployeeID')

            # Check if row has EmployeeID
            if not pd.isna(employee_id) and employee_id is not None:
                # Process row with EmployeeID (existing logic)
                processed_count += 1

                # Get work orders for this employee
                work_orders = employee_work_orders.get(employee_id, [])

                if not work_orders:
                    # No active work orders found for this employee
                    append_error_message(df_result, idx, f"No active work order found for this employee. Suggestions: fix name or check work order in Odoo.")
                    not_found_count += 1
                    continue

                if len(work_orders) == 1:
                    # Single work order - assign directly, but verify customer ID if available
                    wo = work_orders[0]

                    # Check if CustomerID matches (if CustomerID column is filled)
                    customer_id = row.get('CustomerID')
                    if customer_id and not pd.isna(customer_id) and customer_id != '':
                        # print(f"Checking if preassigned CustomerID matches: {customer_id} vs {wo['CustomerID']}")
                        if int(wo['CustomerID']) != int(customer_id):
                            # Customer IDs don't match - add warning but still assign work order
                            print(f"Customer mismatch: Timesheet shows {customer_id} but work order is for {wo['CustomerID']}")
                            customer_name = row.get('CustomerName', 'Unknown')
                            # append_error_message(df_result, idx, f"Warning - CustomerID: CustomerID mismatch. Timesheet shows '{customer_name}:{customer_id}' but work order is for '{wo['CustomerName']}:{wo['CustomerID']}'.")
                            df_result.at[idx, 'WarningMessage'] = f"CustomerID mismatch. Timesheet shows '{customer_name}:{customer_id}' but work order is for '{wo['CustomerName']}:{wo['CustomerID']}'. Please verify and update the work order in Odoo if necessary."

                    # Always assign CustomerID and CustomerName from the work order
                    df_result.at[idx, 'CustomerID'] = wo['CustomerID']
                    df_result.at[idx, 'CustomerName'] = wo['CustomerName']
                    df_result.at[idx, 'ProjectID'] = wo['ProjectID']
                    df_result.at[idx, 'ProjectName'] = wo['ProjectNumber']
                    df_result.at[idx, 'WorkOrderID'] = wo['WorkOrderID']
                    df_result.at[idx, 'WorkOrderNumber'] = wo['WorkOrderNumber']
                    matched_count += 1
                    continue

                # Multiple work orders - need to narrow down
                filtered_work_orders = work_orders.copy()

                # First try: match by CustomerID if available and column is filled
                customer_id = row.get('CustomerID')
                if customer_id and not pd.isna(customer_id) and customer_id != '':
                    customer_matches = [wo for wo in filtered_work_orders if wo['CustomerID'] == customer_id]
                    if customer_matches:
                        filtered_work_orders = customer_matches

                        if len(filtered_work_orders) == 1:
                            # Single match after customer filtering
                            wo = filtered_work_orders[0]
                            df_result.at[idx, 'CustomerID'] = wo['CustomerID']
                            df_result.at[idx, 'CustomerName'] = wo['CustomerName']
                            df_result.at[idx, 'ProjectID'] = wo['ProjectID']
                            df_result.at[idx, 'ProjectName'] = wo['ProjectNumber']
                            df_result.at[idx, 'WorkOrderID'] = wo['WorkOrderID']
                            df_result.at[idx, 'WorkOrderNumber'] = wo['WorkOrderNumber']
                            matched_count += 1
                            continue

                # Second try: match by date if still multiple matches
                if len(filtered_work_orders) > 1:
                    row_date = row.get('Date')
                    if row_date and not pd.isna(row_date):
                        # Convert row date to datetime if it's not already
                        if isinstance(row_date, str):
                            try:
                                row_date = pd.to_datetime(row_date)
                            except:
                                pass  # If conversion fails, skip date matching

                        if isinstance(row_date, pd.Timestamp):
                            date_matches = []
                            for wo in filtered_work_orders:
                                start_date = wo.get('StartDate')
                                end_date = wo.get('AnticipatedEndDate')

                                # Convert dates if they're strings
                                if isinstance(start_date, str):
                                    try:
                                        start_date = pd.to_datetime(start_date)
                                    except:
                                        start_date = None
                                if isinstance(end_date, str):
                                    try:
                                        end_date = pd.to_datetime(end_date)
                                    except:
                                        end_date = None

                                # Check if row date falls within work order date range
                                if start_date and row_date >= start_date:
                                    if end_date is None or row_date <= end_date:
                                        date_matches.append(wo)

                            if date_matches:
                                filtered_work_orders = date_matches

                                if len(filtered_work_orders) == 1:
                                    # Single match after date filtering
                                    wo = filtered_work_orders[0]
                                    df_result.at[idx, 'CustomerID'] = wo['CustomerID']
                                    df_result.at[idx, 'CustomerName'] = wo['CustomerName']
                                    df_result.at[idx, 'ProjectID'] = wo['ProjectID']
                                    df_result.at[idx, 'ProjectName'] = wo['ProjectNumber']
                                    df_result.at[idx, 'WorkOrderID'] = wo['WorkOrderID']
                                    df_result.at[idx, 'WorkOrderNumber'] = wo['WorkOrderNumber']
                                    matched_count += 1
                                    continue

                # Still multiple matches - save them for user selection
                work_order_ids = [str(wo['WorkOrderID']) for wo in filtered_work_orders]
                df_result.at[idx, 'PossibleWorkOrders'] = ','.join(work_order_ids)
                multiple_matches_count += 1

            elif pd.isna(employee_id) or employee_id is None:
                # Handle rows without EmployeeID but with PossibleEmployees
                possible_employees = row.get('PossibleEmployees')

                if possible_employees and isinstance(possible_employees, list) and len(possible_employees) > 0:
                    # Process each possible employee to find work orders
                    all_possible_work_orders = []

                    for emp_dict in possible_employees:
                        emp_id = emp_dict.get('EmployeeID')
                        emp_name = emp_dict.get('EmployeeName')

                        if emp_id:
                            # Get work orders for this possible employee
                            emp_work_orders = employee_work_orders.get(emp_id, [])

                            # Apply the same filtering logic as above
                            filtered_work_orders = emp_work_orders.copy()

                            # First try: match by CustomerID if available
                            customer_id = row.get('CustomerID')
                            if customer_id and not pd.isna(customer_id) and customer_id != '':
                                customer_matches = [wo for wo in filtered_work_orders if wo['CustomerID'] == customer_id]
                                if customer_matches:
                                    filtered_work_orders = customer_matches

                            # Second try: match by date if still multiple matches
                            if len(filtered_work_orders) > 1:
                                row_date = row.get('Date')
                                if row_date and not pd.isna(row_date):
                                    # Convert row date to datetime if it's not already
                                    if isinstance(row_date, str):
                                        try:
                                            row_date = pd.to_datetime(row_date)
                                        except:
                                            pass  # If conversion fails, skip date matching

                                    if isinstance(row_date, pd.Timestamp):
                                        date_matches = []
                                        for wo in filtered_work_orders:
                                            start_date = wo.get('StartDate')
                                            end_date = wo.get('AnticipatedEndDate')

                                            # Convert dates if they're strings
                                            if isinstance(start_date, str):
                                                try:
                                                    start_date = pd.to_datetime(start_date)
                                                except:
                                                    start_date = None
                                            if isinstance(end_date, str):
                                                try:
                                                    end_date = pd.to_datetime(end_date)
                                                except:
                                                    end_date = None

                                            # Check if row date falls within work order date range
                                            if start_date and row_date >= start_date:
                                                if end_date is None or row_date <= end_date:
                                                    date_matches.append(wo)

                                        if date_matches:
                                            filtered_work_orders = date_matches

                            # Add all filtered work orders for this employee to the possible work orders
                            for wo in filtered_work_orders:
                                work_order_dict = {
                                    'WorkOrderID': wo['WorkOrderID'],
                                    'WorkOrderNumber': wo['WorkOrderNumber'],
                                    'ProjectID': wo['ProjectID'],
                                    'ProjectNumber': wo['ProjectNumber'],
                                    'EmployeeID': emp_id,
                                    'FullName': emp_name,
                                    'CustomerID': wo['CustomerID'],
                                    'CustomerName': wo['CustomerName']
                                }
                                all_possible_work_orders.append(work_order_dict)

                    # Store the list of possible work orders
                    if all_possible_work_orders:
                        df_result.at[idx, 'PossibleWorkOrders'] = all_possible_work_orders
                        multiple_matches_count += 1
                    else:
                        append_error_message(df_result, idx, f"No active work orders found for any of the possible employees.")
                        not_found_count += 1

        print(f"Work order processing complete:")
        print(f"  Processed: {processed_count} rows")
        print(f"  Matched: {matched_count} work orders")
        print(f"  Multiple matches: {multiple_matches_count} rows")
        print(f"  Not found: {not_found_count} rows")

        return df_result