<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" rel="icon" type="image/png">
        <link href="https://cdn.jsdelivr.net/npm/remixicon@2.2.0/fonts/remixicon.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">        
        <link href="{{ url_for('static', filename='css/styles_odoo.css') }}" rel="stylesheet" type="text/css"/>
        {% block styles %}{% endblock %}
        <style>
            div#loading {
                width: 500px;
                height: 500px;
                display: none;
                background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
                background-size: contain;
                cursor: wait;
                z-index: 1000;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                
                box-shadow: none; /* Ensure no shadow is applied */
                filter: none; /* Remove any filters that might create a shadow effect */
            }
        </style>
    </head>
    <body>
        <div id="stepLabel" class="fadeIn text-center mt-4"></div>
        <div id="stepProgress" class="progress w-75 position-absolute start-50 translate-middle" role="progressbar" aria-valuemin="0" aria-valuemax="100">            
            <div class="progress-bar bg-success"></div>
        </div>
        <body class="bg-gradient-white">
            <div id="loading"></div>
            <div id="content" class="wrapper fadeIn">
                {% block content %} {% endblock %}
            </div>
        </body>
        <div id="spinnerModal" class="modal" tabindex="-1" aria-labelledby="spinnerModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">Processing...</p>
                    </div>
                </div>
            </div>
        </div>
        {% block modal %} {% endblock %}
    </body>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js" integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy" crossorigin="anonymous"></script> 
    {% block scripts %}{% endblock %}
    <script type="text/javascript">
        function loading() {
            $("#loading").show();
            $("#content").hide(); 
        }

        function doneLoading() {
            $("#loading").hide();
            $("#content").show(); 
        }

        function processErrors(errors, nameErrors, redirect_url, fileIds) {
            originalErrors = errors;  // Store the original errors
            originalNameErrors = nameErrors;  // Store the original errors
            let travelEntries = [];

            // Check errors array
            errors.forEach((error, errorIndex) => {
                console.log(`Checking error at index ${errorIndex}:`, error);
                
                if (!error.Hours || !Array.isArray(error.Hours)) {
                    console.error(`Invalid Hours property at error index ${errorIndex}:`, error.Hours);
                    return; // Skip this error if Hours is not present or not an array
                }

                error.Hours.forEach((hour, hourIndex) => {
                    console.log(`Checking hour at index ${hourIndex}:`, hour);
                    
                    if (typeof hour.UnknownTravel === 'undefined') {
                        console.error(`Invalid UnknownTravel property at error index ${errorIndex}, hour index ${hourIndex}:`, hour);
                        return; // Skip this hour if UnknownTravel is not present
                    }

                    if (hour.UnknownTravel) {
                        travelEntries.push({
                            "Name": error.Employee,
                            "Date": hour.Date,
                            "Hours": hour.Hours,
                            "FileName": error.FileName,
                            "ErrorIndex": errorIndex,  // Store the index for later reference
                            "HourIndex": hourIndex,
                            "Type": "error"
                        });
                    }
                });
            });

            // Check nameErrors array
            nameErrors.forEach((error, errorIndex) => {            
                if (!error.Hours || !Array.isArray(error.Hours)) {
                    console.error(`Invalid Hours property at nameError index ${errorIndex}:`, error.Hours);
                    return; // Skip this error if Hours is not present or not an array
                }

                error.Hours.forEach((hour, hourIndex) => {
                    
                    if (typeof hour.UnknownTravel === 'undefined') {
                        console.error(`Invalid UnknownTravel property at nameError index ${errorIndex}, hour index ${hourIndex}:`, hour);
                        return; // Skip this hour if UnknownTravel is not present
                    }

                    if (hour.UnknownTravel) {
                        travelEntries.push({
                            "Name": error.OriginalName,
                            "Date": hour.Date,
                            "Hours": hour.Hours,
                            "FileName": error.FileName,
                            "ErrorIndex": errorIndex,  // Store the index for later reference
                            "HourIndex": hourIndex,
                            "Type": "name_error"
                        });
                    }
                });
            });
            // If a travel entry found, prompt the user
            if (travelEntries.length > 0) {
                promptUserForTravelType(travelEntries, redirect_url, fileIds);
            } else {
                // Continue processing if no UnknownTravel entries
                continueProcessing(redirect_url);
            }
        }

        // Function to populate the modal table
        function promptUserForTravelType(travelEntries, redirect_url, fileIds) {
            const tbody = document.querySelector('#travelTable tbody');
            tbody.innerHTML = ''; // Clear any existing rows

            let currentName = '';
            let currentColor = 'colorA'; // Initial color

            const colorA = '#cce5ff'; // Light blue
            const colorB = '#99ccff'; // Darker blue

            travelEntries.forEach((entry, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="vertical-align: middle;">${entry.Name}</td>
                    <td style="vertical-align: middle;">${new Date(entry.Date).toLocaleDateString()}</td>
                    <td style="vertical-align: middle;">${parseFloat(entry.Hours).toFixed(2)}</td>
                    <td style="vertical-align: middle;">
                        <select class="form-control" data-entry-index="${index}">
                            <option value="Arrive">Arrive</option>
                            <option value="Depart">Depart</option>
                            <option value="Working">Working</option>
                        </select>
                    </td>
                    <td style="vertical-align: middle;">${entry.FileName}</td>
                    <td style="display: none;">${entry.ErrorIndex}</td>
                    <td style="display: none;">${entry.HourIndex}</td>
                    <td style="display: none;">${entry.Type}</td>
                `;

                if (entry.Name !== currentName) {
                    currentName = entry.Name;
                    currentColor = (currentColor === 'colorA') ? 'colorB' : 'colorA';
                }

                row.style.backgroundColor = (currentColor === 'colorA') ? colorA : colorB;
                tbody.appendChild(row);
            });

            $('#confirmationModal').modal({
                backdrop: 'static', // Prevent clicking outside the modal to close
                keyboard: false // Prevent closing the modal with keyboard ESC key
            });

            window.fileIds = fileIds; // Store fileIds in a global variable for access in handleCancel
            window.redirect_url = redirect_url; // Store redirect_url in a global variable for access in handleCancel
        }

        // Function to continue processing
        function continueProcessing(redirect_url) {
            console.log('All travel entries processed. Continuing with final steps.');
            // Implement your final processing logic here
            // Store errors in sessionStorage or localStorage
            if (originalErrors.length > 0 || originalNameErrors.length > 0) {
                sessionStorage.setItem('errors', JSON.stringify(originalErrors));
                sessionStorage.setItem('name_errors', JSON.stringify(originalNameErrors));
                window.location.href = redirect_url;
            }
            else {
                window.location.href = '/upload_customer_timesheets_ai';
            }
        }

    </script>
</html>