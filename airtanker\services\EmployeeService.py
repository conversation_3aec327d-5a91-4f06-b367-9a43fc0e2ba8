from services.OdooService import OdooService
from nameparser import <PERSON><PERSON>ame
from datetime import datetime, timedelta
from services.DatabaseService import DatabaseService
from fuzzywuzzy import process, fuzz
from main import airtanker_app
from datetime import datetime, date
from dateutil import parser
from typing import List, Optional, Dict
from models.NameMatch import NameMatch, Status, Origin, normalize_name


class EmployeeService:
    '''
    Employee name matching service similar to CustomerService.
    What this function will do:
        1) Try an exact match between the employee name in the employee_names and the employee name in the active work orders.
            1.1) If exact match is found, return [{employee_id: employee_name}] from the active work orders.
        2) Fuzzy match employees from employee_names to employees in active work orders over 70%
            2.1) If results over 90% == 1, return the highest match [{employee_id: employee_name}] from the active work orders.
            2.2) If results over 70% > 1, return the list of employees.
            2.3) If no results over 70%, return None.
        3) Partial match employees from employee_names to employees in active work orders with misspellings tolerance.
        4) Retrieve all employees from database
            4.1) Try an exact match between the employee name in the employee_names and the employee name in the database.
            4.2) If exact match is found, return [{employee_id: employee_name}] from the database.
            4.3) If no exact match, try partial match from the database.
            4.4) Fuzzy match employees from employee_names to employees in database over 70%
                4.4.1) If results over 90% == 1, return the highest match [{employee_id: employee_name}] from the database.
                4.4.2) If results over 70% > 1, return the list of employees.
                4.4.3) If no results over 70%, return None.
    '''

    def __init__(self, database_service: DatabaseService):
        self.database_service = database_service

    def match_employees(self, employee_names: List[str], active_wos_employees: List[Dict[str, any]]) -> List[NameMatch]:
        """
        Match employee names against active work orders and database employees.

        Args:
            employee_names: List of employee names to match
            active_wos_employees: List of dicts with employee_id and employee_name from active work orders

        Returns:
            List of NameMatch objects with matching results
        """
        results = []

        for employee_name in employee_names:
            match_result = self._match_single_employee(
                employee_name,
                active_wos_employees,
            )
            results.append(match_result)

        return results

    def _match_single_employee(self, employee_name: str, active_wos_employees: List[Dict[str, any]]) -> NameMatch:
        """Match a single employee name through the multi-step process."""

        # Step 1: Exact match in active work orders
        exact_match = self._find_exact_match(employee_name, active_wos_employees)
        if exact_match:
            return NameMatch(
                name=employee_name,
                status=Status.MATCHED,
                origin=Origin.ACTIVE_WO,
                ids=[exact_match['employee_id']]
            )

        # Step 2: Fuzzy match in active work orders
        fuzzy_result = self._fuzzy_match_employees(employee_name, active_wos_employees, Origin.ACTIVE_WO)
        if fuzzy_result:
            return fuzzy_result
        
        # Step 3: Partial match in active work orders
        partial_match = self._find_partial_match(employee_name=employee_name, employees=active_wos_employees)
        if partial_match:
            return NameMatch(
                name=employee_name,
                status=Status.MATCHED,
                origin=Origin.ACTIVE_WO,
                ids=[partial_match['employee_id']]
            )

        # Step 4: Search in database
        try:
            all_db_employees = self.database_service.get_all_employees()
            remapped_db_employees = [
                {'employee_id': employee['EmployeeID'], 'employee_name': employee['FullName']}
                for employee in all_db_employees
            ]

            # Step 4.1: Exact match in database
            exact_db_match = self._find_exact_match(employee_name, remapped_db_employees)
            if exact_db_match:
                return NameMatch(
                    name=employee_name,
                    status=Status.MATCHED,
                    origin=Origin.DATABASE,
                    ids=[exact_db_match['employee_id']]
                )
            
            # Step 4.3: Partial match in database
            partial_db_match = self._find_partial_match(employee_name=employee_name, employees=remapped_db_employees)
            if partial_db_match:
                return NameMatch(
                    name=employee_name,
                    status=Status.MATCHED,
                    origin=Origin.DATABASE,
                    ids=[partial_db_match['employee_id']]
                )

            # Step 4.2-4.4: Fuzzy match in database
            db_fuzzy_result = self._fuzzy_match_employees(employee_name, remapped_db_employees, Origin.DATABASE)
            if db_fuzzy_result:
                return db_fuzzy_result

        except Exception as e:
            # Handle database errors gracefully
            print(f"Database error while matching {employee_name}: {e}")

        # No matches found
        return NameMatch(
            name=employee_name,
            status=Status.NOT_FOUND,
            origin=None,
            ids=None
        )

    def _find_exact_match(self, employee_name: str, employees: List[Dict[str, any]]) -> Optional[Dict[str, any]]:
        """Find exact match (case-insensitive) in employee list."""
        for employee in employees:
            if employee['employee_name'].lower() == employee_name.lower():
                return employee
        return None

    def _fuzzy_match_employees(self, employee_name: str, employees: List[Dict[str, any]], origin: Origin) -> Optional[NameMatch]:
        """
        Perform fuzzy matching on employee list.

        Returns:
            NameMatch with MATCHED status if single match >90%
            NameMatch with AMBIGUOUS status if multiple matches >70%
            None if no matches >70%
        """
        matches = []

        for employee in employees:
            similarity = fuzz.ratio(
                employee_name.lower(),
                employee['employee_name'].lower()
            )

            if similarity > 70:
                matches.append({
                    'employee': employee,
                    'similarity': similarity
                })

        if not matches:
            return None

        # Sort by similarity (highest first)
        matches.sort(key=lambda x: x['similarity'], reverse=True)

        # Check for matches over 90%
        high_confidence_matches = [m for m in matches if m['similarity'] > 90]

        if len(high_confidence_matches) == 1:
            return NameMatch(
                name=employee_name,
                status=Status.MATCHED,
                origin=origin,
                ids=[high_confidence_matches[0]['employee']['employee_id']]
            )

        # Multiple matches over 70% (ambiguous)
        if len(matches) > 1:
            return NameMatch(
                name=employee_name,
                status=Status.AMBIGUOUS,
                origin=origin,
                ids=[m['employee']['employee_id'] for m in matches]
            )

        # Single match over 70% but under 90%
        return NameMatch(
            name=employee_name,
            status=Status.AMBIGUOUS,
            origin=origin,
            ids=[matches[0]['employee']['employee_id']]
        )

    def _find_partial_match(self, *, employee_name: str, employees: List[Dict[str, any]]) -> Optional[Dict[str, any]]:
        """
        Find partial match where all words from employee_name appear in the full name.
        Handles misspellings using fuzzy matching on individual words.
        
        Example: 'Luis Padilla' matches 'Luis Alonso Padilla Gonzalez'
        """
        print(f"Trying partial match for {employee_name}")
        import re
        
        # Split the input name into words and clean them
        input_words = [word.strip().lower() for word in re.split(r'\s+', employee_name) if word.strip()]
        
        if not input_words:
            print(f"  No input words found for {employee_name}")
            return None
        
        for employee in employees:
            full_name = employee['employee_name'].lower()
            full_name_words = [word.strip() for word in re.split(r'\s+', full_name) if word.strip()]
            
            # Check if all input words can be matched in the full name
            matched_words = 0
            
            for input_word in input_words:
                word_matched = False
                
                # First try exact match
                if input_word in full_name_words:
                    word_matched = True
                    # print(f"  Exact match found for {input_word} in {full_name}")
                else:
                    # Try fuzzy match for misspellings (85% threshold)
                    for full_word in full_name_words:
                        if fuzz.ratio(input_word, full_word) >= 85:
                            word_matched = True
                            # print(f"  Fuzzy match found for {input_word} in {full_word}")
                            break
                
                if word_matched:
                    matched_words += 1
            
            # If all input words were matched, this is a valid partial match
            if matched_words == len(input_words):
                print(f"Partial match found for {employee_name} in {full_name}")
                return employee
        
        print(f"No partial match found for {employee_name}")
        return None


def parse_employees(sheet, file_id,
                    file_name, 
                    sheet_name, 
                    error_entry_id_counter, 
                    selected_week_ending,
                    odooService, # type: OdooService
                    database_service, # type: DatabaseService
                    all_active_work_order_entries):
    '''This parse the Odoo employee timesheet file and adds 
    their data to the database.'''

    errors = []
    name_errors = []
    
    # Set columns
    date_col = 1
    employee_name_col = 2
    job_num_col = 3 # This is the Project ID / Description. Need to split to parse.
    #job_desc_col = 3  # We're gonna get this from the same column with split.
    #op_code_col = 4 # Odoo doesn't have an Op Code.
    customer_col = 4
    task_col = 5
    hour_col = 6

    # Extract just the names from all_employee_names for matching
    names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])

    # TODO Get all the ProjectIDs
    # TODO Get all the EmployeeIDs
    # TODO Get all the TaskIDs

    # Parse the worksheet
    rowCount = 1

    projects = odooService.get_all_projects()
    employees = odooService.get_all_employees()    

    for row in sheet.iter_rows(min_row=2, values_only=True): # skip the headers        
        rowCount += 1
        if row[0] == "Grand Totals": # skip from grand total at the end 
            break
        
        try:
            customer_name = row[customer_col - 1] # type: str
            # Parse the date to get the year            
            if customer_name.upper() == "ATOMTECH":
                # TODO add the data to the database
                project_name = row[2] # type: str
                if project_name.strip() == "Administration":
                    continue

                date = row[0]
                year = date.year

                # Update mappings with the extracted year
                mappings = {
                    "Development": f"{year} Dev. Hours",
                    "Hardware Design": f"{year} Dev. Hours",
                    "Software Design": f"{year} Dev. Hours",
                    "Emergency Support": "Emergency Support Hours"
                }

                project_id = None
                task_id = None
                employee_id = None

                employee_name = row[1] # type: str
                task_name = row[4] # type: str

                for project in projects:
                    if project['name'] == project_name:
                        project_id = project['id']
                        tasks = odooService.get_all_tasks(project)
                        if len(tasks) > 1:
                            for task in tasks:
                                if task['name'] == task_name:
                                    task_id = task['id']
                                    break
                            if (mapping_name := mappings.get(task_name)) is not None and not task_id:
                                for task in tasks:
                                    if task['name'] == mapping_name:
                                        task_id = task['id']
                                        break
                        elif len(tasks) == 1:
                            task_id = tasks[0]['id']
                        break

                for employee in employees:
                    if employee['name'] == employee_name:
                        employee_id = employee['id']
                        break

                # Add the employee to the employees table and then add their hours to the internal timesheets table
                database_service.find_or_add_employee(HumanName(employee_name), "Internal", employee_id) # Insert into Employees table. 

                # Example date in row[0], assuming it is a string and needs to be converted to a datetime object
                #date_obj = row[0]
                #date_obj = datetime.strptime(date_str, '%Y-%m-%d')  # Adjust the date format as necessary

                # Calculate the week-ending date
                week_ending_date = calculate_week_ending(row[0])

                # Updated insert query to include the WeekEnding column
                insert_query = """
                    INSERT INTO [dbo].[Internal_Timesheets] 
                        ([Date], [EmployeeID], [EmployeeName], [ProjectID], [ProjectName], 
                        [TaskID], [TaskName], [Description], [Hours], [FileID], [WeekEnding])
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                # Add the week_ending_date to the parameters tuple
                parameters = (row[0], employee_id, employee_name, project_id, project_name, task_id, task_name, row[4], row[5], file_id, week_ending_date)

                # Execute the insert query with the parameters
                database_service.execute_query(insert_query, parameters)
                continue

            employee_name = HumanName(row[employee_name_col - 1])
            task_name = row[task_col - 1]
            task_id = database_service.find_or_create_task(task_name=task_name)
            reported_project_number = row[job_num_col - 1]

            # Find the best match for employee_name in names_to_match_against
            best_match_name, score = process.extractOne(employee_name.full_name, names_to_match_against)

            ## If the name doesn't match well
            no_name_found_error = False
            if score < 90:
                no_name_found_error, name_error = get_name_error(all_active_work_order_entries,
                                                                employee_name,
                                                                names_to_match_against,
                                                                name_errors,
                                                                error_entry_id_counter,
                                                                file_name)                               
                if name_error:
                    name_errors.append(name_error)
                    error_entry_id_counter += 1
            else:
                emps_work_orders = []
                project_numbers = []
                for wo_entry in all_active_work_order_entries:
                    if wo_entry["FullName"] == best_match_name:
                        emps_work_orders.append(wo_entry)
                        curr_project_number = wo_entry['ProjectNumber'].strip()
                        project_numbers.append(curr_project_number)

            curr_employee_id = emps_work_orders[0]['EmployeeID']

            # Get current date && hours for the date
            curr_date = row[date_col - 1]
            hours = float(row[hour_col - 1])

            # Locate the correct work order - they might have more than 1 active.
            best_match_project_number, score = process.extractOne(reported_project_number, project_numbers) # Check which WO it is, if multiple.
            
            if no_name_found_error:
                matching_entry = None
                for error_entry in name_errors:
                    if error_entry['OriginalName'] == employee_name.full_name:
                        matching_entry = error_entry
                        break  # Stop searching once a match is found
                if matching_entry:
                    data = {
                        'Date':curr_date,
                        'FileID':file_id,
                        'Hours':hours,
                        'TaskID':task_id
                    }
                    matching_entry["Hours"].append(data)

            elif score < 90:
                work_order_entry = {
                }
                for entry in emps_work_orders:
                    if entry['ProjectNumber'].strip() in work_order_entry:
                        # append it
                        work_order_entry[entry['ProjectNumber'].strip()].append({
                            entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                            })
                    else:
                        work_order_entry[entry["ProjectNumber"].strip()] = [{
                            entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                        }]

                error = {
                    'ID':error_entry_id_counter,
                    'FileName': file_name,
                    'Message': f"Employee Reported Project Number: '{reported_project_number}' isn't found in any assigned work orders for selected week ending {selected_week_ending}",
                    'ReportedProjectNumber':reported_project_number,
                    'WorkOrderEntry':work_order_entry,
                    'Employee': employee_name.full_name,
                    'EmployeeID':curr_employee_id,
                    'Hours': [
                        {'Date': curr_date, 'TaskID': task_id, 'Hours': hours, 'FileID':file_id,
                    },
                        # More entries as needed
                    ]
                }

                # Iterate over the list of errors to find the match
                matching_entry = None
                for error_entry in errors:
                    if error_entry['EmployeeID'] == curr_employee_id and error_entry['ReportedProjectNumber'] == reported_project_number:
                        matching_entry = error_entry
                        break  # Stop searching once a match is found
                if matching_entry:
                    # If found, add to the current hours
                    hours_match = None
                    for hours_entry in matching_entry['Hours']:
                        if hours_entry['Date'] == curr_date and hours_entry['TaskID'] == task_id:
                            hours_match = hours_entry
                            break  # Stop searching once a match is found

                    if hours_match:
                        hours_match['Hours'] += hours                      
                    else:
                        matching_entry['Hours'].append({
                            'Date':curr_date,
                            'TaskID':task_id,
                            'Hours':hours,
                            'FileID':file_id
                        })
                else:
                    errors.append(error)
                    # Increase the ID counter after each new entry into the error logs
                    error_entry_id_counter += 1
                continue
        
            # Handle Multiple Work Order Entries
            elif len(emps_work_orders) > 1:
                # Check if there's a timesheet with that data already. Use that WorkOrder.
                prev_timesheet_found = False

                employee_id = emps_work_orders[0]["EmployeeID"]
                query = """
                    SELECT TOP (1) [WorkOrderID]
                    FROM [dbo].[EmployeeReportedHours]
                    WHERE [EmployeeID] = ? AND Date = ?
                """
                self_reported_hours = database_service.execute_query(query, (employee_id, curr_date))
                if self_reported_hours:
                    prev_timesheet_found = True
                    work_order_id_from_reported_hours = self_reported_hours[0]["WorkOrderID"]
                    result = next((entry for entry in all_active_work_order_entries if entry["WorkOrderID"] == work_order_id_from_reported_hours), None)

                    project_id = result['ProjectID']
                    customer_id = result['CustomerID']
                    work_order_id = result['WorkOrderID']               
                    database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                    date=curr_date, 
                                                                    customer_reported_hours=hours,
                                                                    project_id=project_id,
                                                                    customer_id=customer_id,
                                                                    file_id=file_id,
                                                                    work_order_id=work_order_id,
                                                                    task_id=None,
                                                                    location_id=None) 
                    continue # to next row in sheet

                # If there's not a previous match of reported hours, check previous errors to see if there is a match,
                # and add the error to make user choose the correct work order.
                if not prev_timesheet_found:
                    # Put into errors
                    work_order_entry = {}
                    for entry in emps_work_orders:
                        if entry['ProjectNumber'].strip() in work_order_entry:
                            # append it
                            work_order_entry[entry['ProjectNumber'].strip()].append({
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                })
                        else:
                            work_order_entry[entry["ProjectNumber"].strip()] = [{
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                            }]
                    error = {
                        'ID':error_entry_id_counter,
                        'FileName': file_name,
                        'Message': f"More than one work order assigned. Please select correct one.",
                        'WorkOrderEntry':work_order_entry,
                        'Employee': employee_name.full_name,
                        'EmployeeID':curr_employee_id,
                        'Hours': [
                            {'Date': curr_date, 'TaskID': task_id, 'Hours': hours, 'FileID':file_id,},
                        ]
                    }
                    errors.append(error)
                    # Increase the ID counter after each new entry into the error logs
                    error_entry_id_counter += 1
                    continue

            # Handle No Work Order
            elif len(emps_work_orders) < 1:
                raise Exception(f'No work orders found for {employee_name.full_name}.')
            
            else:
                # if not name error, if project ID matches, if employee has only 1 work order currently, then add hours.
                for wo_entry in emps_work_orders:
                    if wo_entry["ProjectNumber"].strip() == best_match_project_number:
                        curr_emp_work_order = wo_entry
                        break

                database_service.insert_hours_internal(employee_id=curr_emp_work_order['EmployeeID'], 
                                                        date=curr_date, 
                                                        employee_reported_hours=hours,
                                                        project_id=curr_emp_work_order['ProjectID'],
                                                        customer_id=curr_emp_work_order['CustomerID'],
                                                        file_id=file_id,
                                                        work_order_id=curr_emp_work_order["WorkOrderID"],
                                                        task_id=task_id,
                                                        location_id=None)

        except Exception as e:
            print(f"Error in file at row {rowCount} with error {e} for {employee_name.full_name}")
            airtanker_app.logger.error(f"Error in file at row {rowCount} with error {e} for {employee_name.full_name}")

    return errors, name_errors, error_entry_id_counter


from datetime import datetime, timedelta

# Function to calculate the next Sunday from a given date
def calculate_week_ending(date):
    days_ahead = 6 - date.weekday()  # Calculate days ahead to reach Sunday
    if days_ahead < 0:  # If the date is Sunday itself, days_ahead will be 0
        days_ahead += 7
    week_ending = date + timedelta(days=days_ahead)
    return week_ending


def get_name_error(all_active_work_order_entries,
                   employee_name,
                   names_to_match_against,
                   name_errors,
                   error_entry_id_counter,
                   file_name):
        
        # Pre-check
        # Check if the row is already in there. No need to add more. We just need to add the hours
        # This is because it's a row by row basis for this timesheet template.
        for error_entry in name_errors:
            if error_entry['OriginalName'] == employee_name.full_name:
                if len(error_entry["Hours"]) > 0:
                    # Do not duplicate entries. The name error is already in there
                    # return that there is a name error though for later in the code
                    # where this function is referenced
                    return True, None                


        # Put into errors
        name_error = None
        matches = process.extract(employee_name.full_name, names_to_match_against, limit=5)
        for match in matches:
            match_name, score = match
            # Get the hours and get the work orders of each
            emps_work_orders = []
            project_numbers = []
            for wo_entry in all_active_work_order_entries:
                if wo_entry["FullName"] == match_name:
                    emps_work_orders.append(wo_entry)
                    curr_project_number = wo_entry['ProjectNumber'].strip()
                    project_numbers.append(curr_project_number)

            work_order_entry = {}
            employee_id = emps_work_orders[0]
            for entry in emps_work_orders:
                if entry['ProjectNumber'].strip() in work_order_entry:
                    # append it
                    work_order_entry[entry['ProjectNumber'].strip()].append({
                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                        })
                else:
                    work_order_entry[entry["ProjectNumber"].strip()] = [{
                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                    }]
                    
            


            if name_error:     
                data = {
                    'EmployeeName':match_name,
                    'EmployeeID':employee_id,
                    'WorkOrders':work_order_entry
                }
                name_error['EmployeeData'].append(data)
            else:
                name_error = {
                    'ID':error_entry_id_counter,
                    'OriginalName':employee_name.full_name,
                    'FileName': file_name,
                    'Message': f"Original Name: <strong>{employee_name.full_name}</strong>. No direct matches in the database. Please select correct employee. If name doesn't exist in dropdown, this could indicate the employee clocked hours against a non-existent work order.",
                    'EmployeeData':[{
                        'EmployeeName':match_name, 'EmployeeID':employee_id, 'WorkOrders':work_order_entry # It's the Project number and work order numbers
                    }],
                    'Hours':[]
                }
        return True, name_error # no_name_found_error, error_not_found_low_score, name_error / new entry         


# Connect to the DB
def push_updates(updated_entries, user):
    database_service = DatabaseService()
    database_service.connect()

    for entry in updated_entries:        
            
        work_order_id = entry['WorkOrderID']
        
        # Filter by WorkOrderID
        get_all_active_work_order_entries_query = """
            SELECT [FullName] 
                ,[WorkOrderID]   
                ,[WorkOrderNumber]
                ,[StartDate]
                ,[AnticipatedEndDate]
                ,[ProjectID]
                ,[ProjectNumber]
                ,[EmployeeID]
                ,[CustomerID]
                ,[CustomerName]
            FROM [dbo].[WorkOrderFullDetails]
            WHERE WorkOrderID = ?
        """
        #        WHERE Status = 1

        # Get all work_order_entries from db where their work order is active
        work_order_data = database_service.execute_query(get_all_active_work_order_entries_query, work_order_id)[0]

        employee_id = work_order_data['EmployeeID']
        project_id = work_order_data['ProjectID']
        customer_id = work_order_data['CustomerID']
        hours_array = entry['Hours']

        ## Check if the entry has an original name, and add to DB
        if (original_name := entry.get('OriginalName')) is not None:
            query = """
                INSERT INTO NameSelections (EmpID, TS_Name, [User], DateSelected)
                VALUES (?, ?, ?, GETDATE())
                """
            parameters = (employee_id, original_name, user)
            database_service.execute_query(query, parameters)


        # get Task, Date, Hours
        for time_entry in hours_array:
            try:
                curr_date = datetime.strptime(time_entry['Date'], '%a, %d %b %Y %H:%M:%S GMT')
            except:
                curr_date = datetime.strptime(time_entry['Date'], '%Y-%m-%d')
            hours = time_entry['Hours']
            task_id = time_entry['TaskID']
            file_id = time_entry['FileID']

            database_service.insert_hours_internal(employee_id=employee_id,
                                                            date=curr_date, 
                                                            employee_reported_hours=hours,
                                                            project_id=project_id,
                                                            customer_id=customer_id,
                                                            file_id=file_id,
                                                            work_order_id=work_order_id,
                                                            task_id=task_id,
                                                            location_id=None)

def push_updates_expenses(updated_entries, user):
    database_service = DatabaseService()
    database_service.connect()
    error_logs = []

    for entry in updated_entries:   
        try:
            work_order_id = entry['WorkOrderID']
            
            # Filter by WorkOrderID
            get_all_active_work_order_entries_query = """
                SELECT [FullName] 
                    ,[WorkOrderID]   
                    ,[WorkOrderNumber]
                    ,[StartDate]
                    ,[AnticipatedEndDate]
                    ,[ProjectID]
                    ,[ProjectNumber]
                    ,[EmployeeID]
                    ,[CustomerID]
                    ,[CustomerName]
                    ,[SiteSheetID]
                FROM [dbo].[WorkOrderFullDetails]
                WHERE WorkOrderID = ?
            """

            # Get all work_order_entries from db where their work order is active, and equals the selected work order id
            work_order_data = database_service.execute_query(get_all_active_work_order_entries_query, work_order_id)[0]

            employee_id = work_order_data['EmployeeID']
            site_sheet_id = work_order_data['SiteSheetID']
            work_order_id = work_order_data['WorkOrderID']
            work_order_number = work_order_data['WorkOrderNumber']
            weekending = entry["WeekEnding"]
            file_id = entry['FileID']

            ## Check if the entry has an original name, and add to DB
            if (original_name := entry.get('OriginalName')) is not None:
                query = """
                    INSERT INTO NameSelections (EmpID, TS_Name, [User], DateSelected)
                    VALUES (?, ?, ?, GETDATE())
                    """
                parameters = (employee_id, original_name, user)
                database_service.execute_query(query, parameters)

            timesheet_query = """
                SELECT  [Source]
                        ,[TimesheetID]
                        ,[WeekEnding]
                        ,[FileName]
                        ,[TimeSheetEntryID]
                        ,[FirstName]
                        ,[LastName]
                        ,[EmployeeID]
                        ,[Date]
                        ,[ReportedHours]
                        ,[WorkOrderNumber]
                        ,[WorkOrderID]
                        ,[SiteSheetID]
                        ,[ProjectNumber]
                        ,[TaskName]
                FROM [dbo].[vw_EmployeeDetailsAllUnion]
                WHERE EmployeeID = ? AND WeekEnding = ? AND WorkOrderID = ? AND (Source = 'Internal' OR Source = 'ADP') 
            """
            parameters = (employee_id, weekending, work_order_id)

            timesheet_results = database_service.execute_query(timesheet_query, parameters)
            
            if not timesheet_results:
                # if not timesheet data for the employee of that week ending, throw error
                # This is checked in the errors though, but here just in case..
                error = {
                        'FileName': entry['FileName'],
                        "Message":f"{work_order_data.get('FullName')} didn't have hours for the week ending {weekending} under work order {work_order_number}. Please upload hours to this work order first, and try again.",
                        'ReportedProjectNumber':'',
                        'WorkOrderEntry':{},
                        'Employee': work_order_data['FullName'],
                        "WorkOrderName": work_order_data['WorkOrderNumber']
                }
                error_logs.append(error)
                database_service.delete_data_with_fileID(fileID=file_id)
                continue
            else:
                timesheet_id = timesheet_results[0]["TimesheetID"]
                ### At this point we have all the data.
                if timesheet_results[0].get("SiteSheetID"): # if true, insert expenses into the database
                    # Check each day based on the reported data

                    # Need to add AirFare 

                    sql_insert = """
                        INSERT INTO [dbo].[ReportedExpenses]
                            ([ExpenseDate]
                            ,[Account]
                            ,[Description]
                            ,[Lodging]
                            ,[RentalCar]
                            ,[PerDiem]
                            ,[Phone]
                            ,[Misc]
                            ,[MileageTotal]
                            ,[FileID]
                            ,[TimesheetEntryID]
                            ,[EmployeeID]
                            ,[WorkOrderID]
                            ,[TimesheetID]
                            ,[JobSite]
                            ,[SiteSheetID])
                        VALUES
                            (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    jobsite = entry["JobSite"]
                    data = entry["Data"]

                    if 'Data' in data:
                        data = data["Data"]

                    for expense_row in data:

                        # skip rows that don't have a date.
                        if expense_row["date"] == None:
                            continue

                        # skip rows that don't have expenses.
                        if is_empty_expense(expense_row):
                            continue

                        date_val = expense_row["date"]
                        if not isinstance(date_val, (datetime, date)):
                            date_val = parse_date(date_val)
                        elif isinstance(date_val, datetime):
                            date_val = date_val.date()

                        if isinstance(date_val, str):
                            error = {
                                'FileName': entry['FileName'],
                                "Message":f"Invalid date format: {date_val} in an entry in this file {entry['FileName']}",
                                'ReportedProjectNumber':'',
                                'WorkOrderEntry':{},
                                'Employee': work_order_data['FullName'],
                                "WorkOrderName": work_order_number
                            }
                            error_logs.append(error)
                            database_service.delete_data_with_fileID(fileID=file_id)
                            raise ValueError(f"Invalid date format: {date_val}")
                        
                        timesheetEntryID = database_service.find_or_create_timesheetEntry(timesheet_id, date=date_val)

                        values = (date_val,
                                expense_row["account"],
                                expense_row["desc"],
                                expense_row["hotel"],
                                expense_row["transport"],
                                expense_row["per_diem"],
                                expense_row["phone"],
                                expense_row["misc"],
                                expense_row["mileage_total"],
                                file_id,
                                timesheetEntryID,
                                employee_id,
                                work_order_id,
                                timesheet_id,
                                jobsite,
                                site_sheet_id)
                        database_service.execute_query(sql_insert, parameters=values)

                    query = """
                        UPDATE [dbo].[Timesheets]
                        SET [ExpensesUploaded] = 1,
                            [Expense_StatusID] = 7
                        WHERE TimesheetID = ?
                    """
                    database_service.execute_query(query, (timesheet_id))
                else: # if no sitesheedID then throw error
                    error = {
                        'FileName': entry['FileName'],
                        "Message":f"WorkOrder {work_order_number} doesn't contain a SiteSheetID. Please add this field to the workorder in Odoo, and try again.",
                        'ReportedProjectNumber':'',
                        'WorkOrderEntry':{},
                        'Employee': work_order_data['FullName'],
                        "WorkOrderName": work_order_number
                    }
                    error_logs.append(error)
                    database_service.delete_data_with_fileID(fileID=file_id)
                    continue
        except Exception as e:
            error_logs.append(e)
            airtanker_app.logger.error(f"error for entry: {entry} Error: {e}")
            continue

    database_service.disconnect()
    return error_logs
    

def is_empty_expense(expense):
    
    keys_to_check = ['hotel', 'transport', 'per_diem', 'phone', 'misc', 'mileage_total']

    for key in keys_to_check:
        value = expense.get(key)
        if value is not None:
            return False
    return True


def parse_date(date_str):
    try:
        return parser.parse(date_str).date()
    except (ValueError, TypeError):
        return date_str
    

def update_travel_hours_for_not_errored_employees(updated_entries, user):
    database_service = DatabaseService()
    database_service.connect()

    for entry in updated_entries:
        work_order_entry = entry['WorkOrderEntry'] # type: dict
        work_order_name = list(work_order_entry.keys())[0]  # get the first key
        work_order_id = list(work_order_entry[work_order_name][0].keys())[0] # get the first element - WorkOrderID

        # Filter by WorkOrderID
        get_all_active_work_order_entries_query = """
            SELECT [FullName] 
                ,[WorkOrderID]   
                ,[WorkOrderNumber]
                ,[StartDate]
                ,[AnticipatedEndDate]
                ,[ProjectID]
                ,[ProjectNumber]
                ,[EmployeeID]
                ,[CustomerID]
                ,[CustomerName]
            FROM [dbo].[WorkOrderFullDetails]
            WHERE WorkOrderID = ?
        """
        # Get all work_order_entries from db where their work order is active
        work_order_data = database_service.execute_query(get_all_active_work_order_entries_query, work_order_id)[0]

        employee_id = work_order_data['EmployeeID']
        project_id = work_order_data['ProjectID']
        customer_id = work_order_data['CustomerID']
        hours_array = entry['Hours']

         # get Task, Date, Hours
        for time_entry in hours_array:
            curr_date = datetime.strptime(time_entry['Date'], '%a, %d %b %Y %H:%M:%S GMT')
            hours = time_entry['Hours']
            task_id = time_entry['TaskID']
            file_id = time_entry['FileID']

            database_service.insert_hours_internal(employee_id=employee_id,
                                                            date=curr_date, 
                                                            employee_reported_hours=hours,
                                                            project_id=project_id,
                                                            customer_id=customer_id,
                                                            file_id=file_id,
                                                            work_order_id=work_order_id,
                                                            task_id=task_id,
                                                            location_id=None)

    database_service.disconnect()


def push_updates_customer(updated_entries, user):
    database_service = DatabaseService()
    database_service.connect()

    for entry in updated_entries:
            
        work_order_id = entry['WorkOrderID']
        
        # Filter by WorkOrderID
        get_all_active_work_order_entries_query = """
            SELECT [FullName] 
                ,[WorkOrderID]   
                ,[WorkOrderNumber]
                ,[StartDate]
                ,[AnticipatedEndDate]
                ,[ProjectID]
                ,[ProjectNumber]
                ,[EmployeeID]
                ,[CustomerID]
                ,[CustomerName]
            FROM [dbo].[WorkOrderFullDetails]
            WHERE WorkOrderID = ?
        """
        #        WHERE Status = 1

        # Get all work_order_entries from db where their work order is active
        work_order_data = database_service.execute_query(get_all_active_work_order_entries_query, work_order_id)[0]
        
        employee_id = work_order_data['EmployeeID']
        project_id = work_order_data['ProjectID']
        customer_id = work_order_data['CustomerID']
        hours = entry['Hours']

        ## Check if the entry has an original name, and add to DB
        if (original_name := entry.get('OriginalName')) is not None:
            query = """
                INSERT INTO NameSelections (EmpID, TS_Name, [User], DateSelected)
                VALUES (?, ?, ?, GETDATE())
                """
            parameters = (employee_id, original_name, user)
            database_service.execute_query(query, parameters)

        # get Task, Date, Hours
        for time_entry in hours:
            rate_type = None
            curr_date = datetime.strptime(time_entry['Date'], '%a, %d %b %Y %H:%M:%S GMT')
            curr_hours = time_entry['Hours']
            file_id = time_entry['FileID']
            if 'RateTypeID' in time_entry:
                rate_type = time_entry['RateTypeID']
            database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id,
                                                            date=curr_date, 
                                                            customer_reported_hours=curr_hours,
                                                            project_id=project_id,
                                                            customer_id=customer_id,
                                                            file_id=file_id,
                                                            work_order_id=work_order_id,
                                                            task_id=None,
                                                            location_id=None,
                                                            rate_type=rate_type)
    
    