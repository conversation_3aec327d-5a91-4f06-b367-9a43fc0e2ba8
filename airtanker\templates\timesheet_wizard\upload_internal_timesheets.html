
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">

    <title>AirTanker</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/styles_internal.css') }}"  />
    <style>
        .modal-dialog.wider-modal {
            max-width: 90%; /* Adjust as needed */
        }
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            position: relative;
            cursor: pointer;
            margin-left: 10px;
            margin-right: 10px;
        }
        /* Style the console-like area */
        #console {
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }
        /* Style different types of messages */
        .error {
            color: red;
        }
        #detailsModal .modal-dialog {
            max-width: 90%;
        }
        /* Add a horizontal scrollbar to the modal body */
        #detailsModal .modal-body {
            overflow-x: auto;
        }
        .file-upload-wrapper:hover {
            background-color: #f3f4f6;
        }
        .file-upload-wrapper i {
            color: #ff6105;            
        }
        .list-group-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-left: 10px;
            margin-right: 10px;
            margin-bottom: 1px;
        }
        .list-group-item i.fa-times {
            color: red;
            cursor: pointer;
        }
        #browse-btn {
            color: blue; /* Set the text color */
            text-decoration: underline; /* Underline the text to mimic a hyperlink */
            cursor: pointer; /* Change the cursor to indicate it's clickable */
        }
        .progress-container {
            width: 80%;
            background-color: #e0e0e0;
            position: fixed; /* Stick to the top */
            z-index: 1000;
            top: 50;
            left: 0;
            right: 0; /* Added to work with margin: auto; */
            margin: auto; /* Center the element */
        }

        
        .progress-bar {
            height: 7px;
            background-color: #4CAF50;
            width: 10%; /* Start state */
            animation-name: loadProgress;
            animation-duration: 2s; /* Customize this value */
            animation-fill-mode: forwards; /* Keeps the state at 66% after animation */
        }
        @keyframes loadProgress {
            from {
                width: 10%;
            }
            to {
                width: 42%;
            }
        }
        div#loading {
            width: 500px;
            height: 500px;
            display: none;
            background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
            background-size: contain;
            cursor: wait;
            z-index: 1000;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            
            box-shadow: none; /* Ensure no shadow is applied */
            filter: none; /* Remove any filters that might create a shadow effect */
        }

    </style>
</head>
<body>
    <!-- Modal for the spinner overlay -->
    <div class="modal" id="spinnerModal" tabindex="-1" role="dialog" aria-labelledby="spinnerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Processing...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    <div id="progressLabel" class="fadeIn" style="display: block; z-index: 1000;text-align: center; position: fixed; width: 100%; top: 20px;">
        Step 2: Upload Employee Provided TimeSheets
    </div>

    <!-- Travel Type Selection -->
    <div class="modal fade" id="confirmationModal" tabindex="-1" role="dialog" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog wider-modal modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmationModalLabel">Select Travel Type</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table table-bordered" id="travelTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Date</th>
                                <th>Hours</th>
                                <th>Travel Type</th>
                                <th>FileName</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Dynamic Rows Will Be Injected Here -->
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="handleCancel(fileIds)" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitTravelForm()">Submit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- The file upload areas -->
    <body class="bg-gradient-white">
        <div id="loading"></div>
        <div class="wrapper fadeIn" id="content">
            <img style="margin-bottom: 10;" src="{{ url_for('static', filename='assets/AtomTech.png') }}" width="200" alt="">
            <div id="formContent" enctype="multipart/form-data">
                <h1 class="h4 text-gray-900 mb-4 fadeIn first" style="margin-top: 5%;">                                            
                    Upload Internal Timesheets
                </h1>
                <div class="file-upload-wrapper" onclick="document.getElementById('file-upload').click()">
                    <i class="fas fa-cloud-upload-alt fa-2x"></i>
                    <p>Drag files here or <a href="#" onclick="handleBrowseClick(event)">Browse</a></p>
                </div>
                <input id="file-upload" type="file" name="uploaded_files[]" multiple style="display: none;" accept=".xlsx, .xlsm" onchange="addFiles()">
                <ul class="list-group mt-3" id="file-list" style=" margin-bottom: 20px;">
                    <!-- Files will be listed here -->
                </ul>                
                <input id="process-files-btn" type="submit" style="display: none;" onclick="processFiles();" value="Process Files">
                <!-- <div class="p-2">
                    <p>OR</p>
                    <a class="underlineHover fadeIn first" href="#" onclick="handlePaycorImport(event)">Import from Paycor</a>
                </div> -->
                <div id="formFooter">
                    <a class="underlineHover fadeIn third" href="/upload_customer_timesheets_ai">Skip This Step</a>
                </div>
                <div id="formFooter">
                    <a class="underlineHover fadeIn third" href="/">Cancel</a>
                </div>
            </div>
        </div>
<!-- 
    <div class="d-flex justify-content-center">
        <button id="process-files-btn" onclick="processFiles()" class="btn btn-primary" style="display:none;margin-bottom: 1px;">Compare</button>
    </div> -->

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    
    <script type="text/javascript">// <![CDATA[
        function loading(){
            $("#loading").show();
            $("#content").hide();       
        }
// ]]></script>
    <script>
    let selectedFiles = [];

    function showProgress(){
        $('#spinnerModal').modal({
        backdrop: 'static', // Prevent clicking outside the modal to close
            keyboard: false // Prevent closing the modal with keyboard ESC key
        });
    }

    function addFiles() {
        var files = document.getElementById('file-upload').files;
        for (var i = 0; i < files.length; i++) {
            selectedFiles.push(files[i]);
        }
        updateFileList();
        document.getElementById('file-upload').value = ''; // Clear the current selection
    }

    function updateFileList() {
        var output = document.getElementById('file-list');
        output.innerHTML = '';
        for (var i = 0; i < selectedFiles.length; ++i) {
            output.innerHTML += '<li class="list-group-item">' +
                                '<i class="fas fa-file-alt"></i> ' +
                                selectedFiles[i].name +
                                '<i class="fas fa-times" onclick="removeFile(' + i + ')"></i>' +
                                '</li>';
        }
        updateVisibilityOfProcessButton();
    }

    function removeFile(index) {
        selectedFiles.splice(index, 1); // Remove the file from the array
        updateFileList(); // Update the list
    }
        
    // Show compare button
    function updateVisibilityOfProcessButton() {
        // Check if both arrays have at least one file
        if (selectedFiles.length > 0 && selectedFiles.length > 0) {
            document.getElementById('process-files-btn').style.display = 'block'; // Show the button
        } else {
            document.getElementById('process-files-btn').style.display = 'none'; // Hide the button
        }
    }            
    
    // Compare button click
    function processFiles() {        
        sessionStorage.removeItem('errors');
        $("#loading").show();
        $("#content").hide();   
        // $('#spinnerModal').modal({
        //     backdrop: 'static', // Prevent clicking outside the modal to close
        //     keyboard: false // Prevent closing the modal with keyboard ESC key
        // });
        
        const formData = new FormData()
        // formData.append(csrf_token());
        selectedFiles.forEach(file=> {
            formData.append(`uploaded_files[]`, file);
        });

        fetch('/upload_internal_timesheets', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if(data.errors || data.name_errors) {
                // process errors
                processErrors(data.errors, data.name_errors, data.redirect_url, data.fileIds);
                console.log("Done Processing");
            }
            else if(data.redirect_url) {
                window.location.href = data.redirect_url;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while processing the files. Please try again.');
        })
        .finally(() => {
            $("#loading").hide();
            $("#content").show();
        });
    };

    function handlePaycorImport(event) {
        event.stopPropagation();
        var requestParams = new URLSearchParams(window.location.search);
        requestParams.append("type","external");
        
        sessionStorage.removeItem('errors');
        $("#loading").show();
        $("#content").hide();

        fetch('/import_paycor_timesheets?' + requestParams.toString(), {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if(data.errors || data.nameErrors) {
                // process errors
                processErrors(data.errors, data.name_errors, data.redirect_url, data.fileIds);
                console.log("Done Processing");
            }
            else if(data.redirect_url) {
                window.location.href = data.redirect_url;
            }
        })
        .finally(() => {
            // $("#loading").hide();
            // $("#content").show();
        })
    }

    function handleBrowseClick(event) {
        event.stopPropagation(); // Stop event propagation to prevent double triggering
        document.getElementById('file-upload').click();
    }

    document.addEventListener("DOMContentLoaded", (event) => {
        setTimeout(() => {
            document.getElementById('progressLabel').style.display = 'block';
        }, 2000); // Match this duration to your CSS animation-duration
    });

    
    let originalErrors = [];  // This will store the original errors
    let originalNameErrors = [];  // This will store the original errors

    // Function to process errors
    function processErrors(errors, nameErrors, redirect_url, fileIds) {
        originalErrors = errors;  // Store the original errors
        originalNameErrors = nameErrors;  // Store the original errors
        let travelEntries = [];

        // Check errors array
        errors.forEach((error, errorIndex) => {
            console.log(`Checking error at index ${errorIndex}:`, error);
            
            if (!error.Hours || !Array.isArray(error.Hours)) {
                console.error(`Invalid Hours property at error index ${errorIndex}:`, error.Hours);
                return; // Skip this error if Hours is not present or not an array
            }

            error.Hours.forEach((hour, hourIndex) => {
                console.log(`Checking hour at index ${hourIndex}:`, hour);
                
                if (typeof hour.UnknownTravel === 'undefined') {
                    console.error(`Invalid UnknownTravel property at error index ${errorIndex}, hour index ${hourIndex}:`, hour);
                    return; // Skip this hour if UnknownTravel is not present
                }

                if (hour.UnknownTravel) {
                    travelEntries.push({
                        "Name": error.Employee,
                        "Date": hour.Date,
                        "Hours": hour.Hours,
                        "FileName": error.FileName,
                        "ErrorIndex": errorIndex,  // Store the index for later reference
                        "HourIndex": hourIndex,
                        "Type": "error"
                    });
                }
            });
        });

        // Check nameErrors array
        nameErrors.forEach((error, errorIndex) => {            
            if (!error.Hours || !Array.isArray(error.Hours)) {
                console.error(`Invalid Hours property at nameError index ${errorIndex}:`, error.Hours);
                return; // Skip this error if Hours is not present or not an array
            }

            error.Hours.forEach((hour, hourIndex) => {
                
                if (typeof hour.UnknownTravel === 'undefined') {
                    console.error(`Invalid UnknownTravel property at nameError index ${errorIndex}, hour index ${hourIndex}:`, hour);
                    return; // Skip this hour if UnknownTravel is not present
                }

                if (hour.UnknownTravel) {
                    travelEntries.push({
                        "Name": error.OriginalName,
                        "Date": hour.Date,
                        "Hours": hour.Hours,
                        "FileName": error.FileName,
                        "ErrorIndex": errorIndex,  // Store the index for later reference
                        "HourIndex": hourIndex,
                        "Type": "name_error"
                    });
                }
            });
        });
        // If a travel entry found, prompt the user
        if (travelEntries.length > 0) {
            promptUserForTravelType(travelEntries, redirect_url, fileIds);
        } else {
            // Continue processing if no UnknownTravel entries
            continueProcessing(redirect_url);
        }
    }


    // Function to populate the modal table
    function promptUserForTravelType(travelEntries, redirect_url, fileIds) {
        const tbody = document.querySelector('#travelTable tbody');
        tbody.innerHTML = ''; // Clear any existing rows

        let currentName = '';
        let currentColor = 'colorA'; // Initial color

        const colorA = '#cce5ff'; // Light blue
        const colorB = '#99ccff'; // Darker blue

        travelEntries.forEach((entry, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td style="vertical-align: middle;">${entry.Name}</td>
                <td style="vertical-align: middle;">${new Date(entry.Date).toLocaleDateString()}</td>
                <td style="vertical-align: middle;">${parseFloat(entry.Hours).toFixed(2)}</td>
                <td style="vertical-align: middle;">
                    <select class="form-control" data-entry-index="${index}">
                        <option value="Arrive">Arrive</option>
                        <option value="Depart">Depart</option>
                        <option value="Working">Working</option>
                    </select>
                </td>
                <td style="vertical-align: middle;">${entry.FileName}</td>
                <td style="display: none;">${entry.ErrorIndex}</td>
                <td style="display: none;">${entry.HourIndex}</td>
                <td style="display: none;">${entry.Type}</td>
            `;

            if (entry.Name !== currentName) {
                currentName = entry.Name;
                currentColor = (currentColor === 'colorA') ? 'colorB' : 'colorA';
            }

            row.style.backgroundColor = (currentColor === 'colorA') ? colorA : colorB;
            tbody.appendChild(row);
        });

        $('#confirmationModal').modal({
            backdrop: 'static', // Prevent clicking outside the modal to close
            keyboard: false // Prevent closing the modal with keyboard ESC key
        });

        window.fileIds = fileIds; // Store fileIds in a global variable for access in handleCancel
        window.redirect_url = redirect_url; // Store redirect_url in a global variable for access in handleCancel
    }


    function handleCancel(fileIds) {        
        fetch('/edit-files', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ fileIDs: fileIds })
        }).then(response => {
            window.fileIds = [];
            if (response.ok) {
                window.location.reload();
            } else {
                console.error('Failed to edit files');
            }
        });
    }


    // Function to handle form submission
    function submitTravelForm() {
        // Fetch task IDs
        fetch('/find_create_task_id', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log(`Original before: `, originalErrors);

            const taskIds = data.results;
            // Initialize an empty array to hold the filtered travel entries
            const filteredTravelEntries = [];

            // For each row in the table after clicking submit button.
            $('#travelTable tbody tr').each((index, row) => {
                const travelType = $(row).find('select').val();
                const entryIndex = $(row).find('select').data('entry-index');
                const errorIndex = parseInt($(row).find('td:nth-child(6)').text()); // assuming ErrorIndex is in 6th hidden column
                const hourIndex = parseInt($(row).find('td:nth-child(7)').text()); // assuming HourIndex is in 7th hidden column
                const type = $(row).find('td:nth-child(8)').text(); // assuming Type is in 8th hidden column
                let taskId = null;

                // Determine the task ID based on the travel type
                if (travelType === 'Arrive') {
                    taskId = taskIds.ArriveTask;
                } else if (travelType === 'Depart') {
                    taskId = taskIds.DepartTask;
                } else if (travelType === 'Working') {
                    taskId = taskIds.SupportHours;
                }
                
                // Update the original errors array based on the type
                if (type === 'error') {
                    originalErrors[errorIndex].Hours[hourIndex].TaskID = taskId;
                    console.log("updated error: ", originalErrors[errorIndex].Hours[hourIndex]);
                } else if (type === 'name_errors') {
                    originalNameErrors[errorIndex].Hours[hourIndex].TaskID = taskId;
                }
            });

            
            // At this point we've successfully modified the original errors array, updating the taskID for each travel entry.
            // We now need to determine which ones to remove from the original errors array. Where error.Message == None


            // Loop through the original errors array
            for (let i = originalErrors.length - 1; i >= 0; i--) {
                // Check if the error's Message is None (null or undefined)
                if (originalErrors[i].Message === null || originalErrors[i].Message === undefined) {
                    // Transfer the element to the filteredTravelEntries array
                    filteredTravelEntries.push(originalErrors[i]);
                    // Remove the element from the originalErrors array
                    originalErrors.splice(i, 1);
                }
            }

            console.log(`filteredTravelEntries: ${JSON.stringify(filteredTravelEntries)}`);
            console.log(`Original after: `, originalErrors);

            // Send the filtered travelEntries back to the server
            return fetch('/update_travel_entries', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(filteredTravelEntries)
            });
        })
        .then(response => {
            if (response.ok) {
                $('#confirmationModal').modal('hide');
                continueProcessing(window.redirect_url);
            } else {
                console.error('Failed to update travel entries');
            }
        })
        .catch(error => {
            console.error('Error fetching task IDs or updating travel entries:', error);
        });
    }

    // Function to continue processing
    function continueProcessing(redirect_url) {
        console.log('All travel entries processed. Continuing with final steps.');
        // Implement your final processing logic here
        // Store errors in sessionStorage or localStorage
        if (originalErrors.length > 0 || originalNameErrors.length > 0) {
            sessionStorage.setItem('errors', JSON.stringify(originalErrors));
            sessionStorage.setItem('name_errors', JSON.stringify(originalNameErrors));
            window.location.href = redirect_url;
        }
        else {
            window.location.href = '/upload_customer_timesheets_ai';
        }
    }

    </script>
</body>
